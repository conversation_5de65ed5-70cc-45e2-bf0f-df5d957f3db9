# 背离交易信号系统

## 概述

本系统实现了基于多技术指标背离的交易信号检测和分析功能，支持RSI、MACD、KDJ等主流技术指标的背离检测，并提供完整的信号生成、验证和可视化功能。

## 主要功能

### 1. 技术指标计算
- **RSI (相对强弱指标)**: 14周期RSI计算，用于判断超买超卖状态
- **MACD (指数平滑移动平均线)**: 包含DIF线、DEA信号线和MACD柱状图
- **KDJ (随机指标)**: 包含K值、D值、J值，用于判断价格动量

### 2. 背离检测系统
- **顶背离检测**: 价格创新高而指标不创新高，预示潜在下跌
- **底背离检测**: 价格创新低而指标不创新低，预示潜在上涨
- **多指标确认**: 支持多个指标同时确认背离信号
- **峰谷检测**: 增强的峰谷检测算法，提供更准确的极值点识别

### 3. 交易信号生成
- **信号过滤**: 最小时间间隔限制，避免过于频繁的信号
- **成交量确认**: 可选的成交量放大确认机制
- **质量评分**: 基于多因素的信号质量评分系统
- **强度计算**: 背离强度量化，帮助判断信号可靠性

### 4. 回测验证
- **完整回测**: 支持买入/卖出信号的完整回测
- **风险控制**: 止损止盈设置，最大持仓时间限制
- **性能指标**: 胜率、收益率、最大回撤、夏普比率等
- **质量分析**: 不同质量信号的表现分析

### 5. 可视化功能
- **背离标记**: 在价格图表上标记背离点
- **连接线**: 显示背离的价格和指标连接线
- **信号点**: 不同颜色和大小表示信号质量和强度
- **交互图表**: 基于Pyecharts的交互式图表

## 使用方法

### 基本使用

```python
from indicator_analysis import StockDataManager, SignalDetector, DivergenceBacktester

# 1. 获取数据
data_manager = StockDataManager()
df = data_manager.get_fund_data("513260.SH", start_date="20240101")

# 2. 生成背离交易信号
buy_signals, sell_signals, stats = SignalDetector.generate_divergence_trading_signals(
    df,
    min_signal_interval=8,
    volume_confirmation=True,
    min_quality_score=0.5,
    indicators=['rsi', 'macd_dif', 'kdj_k']
)

# 3. 执行回测
backtester = DivergenceBacktester(initial_capital=100000)
results = backtester.backtest_divergence_signals(
    df, buy_signals, sell_signals,
    holding_period=15,
    stop_loss_pct=0.08,
    take_profit_pct=0.15
)

# 4. 生成可视化
from indicator_analysis import StockEchartVisualizer
visualizer = StockEchartVisualizer()
chart = visualizer.plot_stock_chart("513260.SH", detect_patterns=True)
chart.render("divergence_analysis.html")
```

### 测试脚本

运行测试脚本验证系统功能：

```bash
# 完整测试
python test_divergence_signals.py

# 简化测试
python simple_divergence_test.py

# 使用示例
python divergence_trading_example.py
```

## 核心算法

### 背离检测算法

1. **峰谷识别**: 使用scipy.signal.argrelextrema结合显著性过滤
2. **极值匹配**: 寻找价格极值对应的指标极值点
3. **背离判断**: 比较价格和指标的变化方向
4. **强度计算**: 量化背离的强度和可靠性

### 信号质量评分

信号质量基于以下因素计算：
- 背离指标数量 (权重: 30%)
- RSI位置确认 (权重: 20%)
- MACD趋势确认 (权重: 15%)
- KDJ状态确认 (权重: 15%)
- 其他技术因素 (权重: 20%)

### 回测策略

- **开仓**: 根据信号类型开多仓或空仓
- **平仓条件**:
  - 达到止损/止盈价位
  - 超过最大持仓时间
  - 出现反向信号
- **资金管理**: 固定资金比例，考虑手续费和滑点

## 参数配置

### 背离检测参数
- `window`: 峰谷检测窗口大小 (默认: 10)
- `min_distance`: 峰谷最小距离 (默认: 15)
- `prominence_threshold`: 显著性阈值 (默认: 0.02)

### 信号生成参数
- `min_signal_interval`: 信号最小间隔 (默认: 8天)
- `volume_confirmation`: 成交量确认 (默认: True)
- `min_quality_score`: 最低质量评分 (默认: 0.5)

### 回测参数
- `initial_capital`: 初始资金 (默认: 100000)
- `commission_rate`: 手续费率 (默认: 0.001)
- `slippage`: 滑点率 (默认: 0.001)
- `holding_period`: 最大持仓天数 (默认: 15)
- `stop_loss_pct`: 止损百分比 (默认: 0.08)
- `take_profit_pct`: 止盈百分比 (默认: 0.15)

## 注意事项

1. **数据质量**: 确保价格数据的完整性和准确性
2. **参数调优**: 根据不同市场环境调整参数
3. **风险控制**: 合理设置止损止盈，控制单笔交易风险
4. **信号确认**: 建议结合多个指标确认，避免单一指标误判
5. **市场环境**: 背离信号在趋势明确的市场中效果更佳

## 文件结构

```
stock_data_analysis/
├── indicator_analysis.py          # 主要功能模块
├── test_divergence_signals.py     # 完整测试脚本
├── simple_divergence_test.py      # 简化测试脚本
├── divergence_trading_example.py  # 使用示例脚本
├── DIVERGENCE_TRADING_README.md   # 本文档
└── cache/                         # 缓存和输出文件
    ├── stock_data_cache.db        # 数据缓存数据库
    └── *.html                     # 生成的图表文件
```

## 更新日志

### v1.0.0 (2024-07-10)
- 实现RSI、MACD、KDJ技术指标计算
- 实现多指标背离检测系统
- 实现交易信号生成和验证机制
- 实现回测验证功能
- 实现可视化功能
- 添加完整的测试和示例脚本
