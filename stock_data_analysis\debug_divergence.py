#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试背离检测的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from indicator_analysis import StockDataManager, SignalDetector
import pandas as pd
import numpy as np

def main():
    print("=" * 60)
    print("背离检测调试")
    print("=" * 60)
    
    # 获取数据
    data_manager = StockDataManager()
    df = data_manager.get_fund_data("512890.SH", start_date="20240101")  # 红利低波ETF
    
    print(f"数据长度: {len(df)}")
    
    # 计算技术指标
    df_with_indicators = SignalDetector._calculate_all_indicators(df.copy())
    
    # 检查技术指标
    print(f"\n技术指标检查:")
    print(f"RSI有效值: {np.sum(~np.isnan(df_with_indicators['rsi']))}")
    print(f"MACD有效值: {np.sum(~np.isnan(df_with_indicators['macd_dif']))}")
    print(f"KDJ有效值: {np.sum(~np.isnan(df_with_indicators['kdj_k']))}")
    
    # 检测背离
    print(f"\n背离检测:")
    divergence_results = SignalDetector.detect_multi_indicator_divergence(
        df_with_indicators,
        indicators=['rsi', 'macd_dif', 'kdj_k'],
        window=8,
        min_distance=10,
        prominence_threshold=0.015
    )
    
    for indicator, results in divergence_results.items():
        bullish_count = results['bullish_divergence'].sum()
        bearish_count = results['bearish_divergence'].sum()
        print(f"{indicator}: 底背离 {bullish_count}个, 顶背离 {bearish_count}个")
        
        # 显示背离发生的日期
        if bullish_count > 0:
            bullish_dates = df_with_indicators[results['bullish_divergence']]['trade_date'].tolist()
            print(f"  底背离日期: {bullish_dates}")
        
        if bearish_count > 0:
            bearish_dates = df_with_indicators[results['bearish_divergence']]['trade_date'].tolist()
            print(f"  顶背离日期: {bearish_dates}")
    
    # 测试信号生成的每个步骤
    print(f"\n信号生成步骤调试:")
    
    # 1. 生成综合信号
    buy_signals, sell_signals = SignalDetector._generate_composite_signals(
        df_with_indicators, divergence_results, ['rsi', 'macd_dif', 'kdj_k']
    )
    print(f"1. 综合信号: 买入 {buy_signals.sum()}个, 卖出 {sell_signals.sum()}个")
    
    # 2. 时间过滤
    buy_signals_filtered = SignalDetector._filter_frequent_signals(buy_signals, 3)
    sell_signals_filtered = SignalDetector._filter_frequent_signals(sell_signals, 3)
    print(f"2. 时间过滤后: 买入 {buy_signals_filtered.sum()}个, 卖出 {sell_signals_filtered.sum()}个")
    
    # 3. 质量评分
    buy_quality = SignalDetector._calculate_divergence_signal_quality(
        df_with_indicators, buy_signals_filtered, 'buy', divergence_results
    )
    sell_quality = SignalDetector._calculate_divergence_signal_quality(
        df_with_indicators, sell_signals_filtered, 'sell', divergence_results
    )
    
    print(f"3. 质量评分: 买入平均 {buy_quality[buy_signals_filtered].mean():.3f}, 卖出平均 {sell_quality[sell_signals_filtered].mean():.3f}")
    
    # 4. 质量过滤
    min_quality = 0.2
    buy_final = buy_signals_filtered & (buy_quality >= min_quality)
    sell_final = sell_signals_filtered & (sell_quality >= min_quality)
    print(f"4. 质量过滤后(>={min_quality}): 买入 {buy_final.sum()}个, 卖出 {sell_final.sum()}个")
    
    # 显示最终信号的日期和质量
    if buy_final.sum() > 0:
        buy_dates = df_with_indicators[buy_final]['trade_date'].tolist()
        buy_qualities = buy_quality[buy_final].tolist()
        print(f"   买入信号详情: {list(zip(buy_dates, [f'{q:.3f}' for q in buy_qualities]))}")
    
    if sell_final.sum() > 0:
        sell_dates = df_with_indicators[sell_final]['trade_date'].tolist()
        sell_qualities = sell_quality[sell_final].tolist()
        print(f"   卖出信号详情: {list(zip(sell_dates, [f'{q:.3f}' for q in sell_qualities]))}")
    
    # 测试信号强度计算
    print(f"\n信号强度计算测试:")
    try:
        buy_strength = SignalDetector._calculate_signal_strength(
            df_with_indicators, buy_final, divergence_results, 'buy'
        )
        sell_strength = SignalDetector._calculate_signal_strength(
            df_with_indicators, sell_final, divergence_results, 'sell'
        )
        print(f"买入信号强度: {buy_strength.tolist()}")
        print(f"卖出信号强度: {sell_strength.tolist()}")
    except Exception as e:
        print(f"信号强度计算失败: {e}")
        import traceback
        traceback.print_exc()

    # 完整的信号生成测试
    print(f"\n完整信号生成测试:")
    try:
        buy_df, sell_df, stats = SignalDetector.generate_divergence_trading_signals(
            df_with_indicators,
            min_signal_interval=3,
            volume_confirmation=False,
            min_quality_score=0.2,
            indicators=['rsi', 'macd_dif', 'kdj_k']
        )

        print(f"最终结果: 买入 {len(buy_df)}个, 卖出 {len(sell_df)}个")
        print(f"统计信息: {stats}")

        if len(buy_df) > 0:
            print(f"买入信号详情:")
            for _, row in buy_df.iterrows():
                print(f"  日期: {row['trade_date']}, 质量: {row.get('quality_score', 'N/A'):.3f}, 强度: {row.get('signal_strength', 'N/A')}")

        if len(sell_df) > 0:
            print(f"卖出信号详情:")
            for _, row in sell_df.iterrows():
                print(f"  日期: {row['trade_date']}, 质量: {row.get('quality_score', 'N/A'):.3f}, 强度: {row.get('signal_strength', 'N/A')}")

    except Exception as e:
        print(f"完整信号生成失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
