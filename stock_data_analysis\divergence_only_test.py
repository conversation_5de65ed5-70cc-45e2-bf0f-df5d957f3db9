#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门测试背离信号的脚本
只显示背离信号，不显示传统EMA/动量信号
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from indicator_analysis import (
    StockDataManager, SignalDetector, DivergenceBacktester, 
    StockEchartVisualizer, TechnicalIndicator
)
import pandas as pd
import numpy as np

def main():
    print("=" * 60)
    print("背离信号专项测试")
    print("=" * 60)
    
    # 初始化数据管理器
    try:
        data_manager = StockDataManager()
        print("[OK] 数据管理器初始化成功")
    except Exception as e:
        print(f"[ERROR] 数据管理器初始化失败: {e}")
        return
    
    # 测试多个基金
    test_funds = {
        "513260.SH": "恒生科技ETF",
        "159509.SZ": "纳指科技ETF", 
        "512890.SH": "红利低波ETF",
        "159302.SZ": "港股高股息ETF"
    }
    
    for fund_code, fund_name in test_funds.items():
        print(f"\n{'='*50}")
        print(f"测试基金: {fund_name} ({fund_code})")
        print(f"{'='*50}")
        
        try:
            # 获取数据
            df = data_manager.get_fund_data(fund_code, start_date="20240101")
            if df.empty:
                print(f"[ERROR] 无法获取 {fund_code} 的数据")
                continue
            
            print(f"[OK] 获取到 {len(df)} 条数据记录")
            
            # 测试不同的参数设置
            test_divergence_with_different_params(df, fund_code, fund_name)
            
        except Exception as e:
            print(f"[ERROR] {fund_code} 测试失败: {e}")

def test_divergence_with_different_params(df, fund_code, fund_name):
    """使用不同参数测试背离检测"""
    
    # 参数组合
    param_sets = [
        {
            "name": "宽松参数",
            "min_signal_interval": 3,
            "min_quality_score": 0.2,
            "window": 8,
            "min_distance": 10,
            "prominence_threshold": 0.015
        },
        {
            "name": "标准参数", 
            "min_signal_interval": 5,
            "min_quality_score": 0.4,
            "window": 10,
            "min_distance": 15,
            "prominence_threshold": 0.02
        },
        {
            "name": "严格参数",
            "min_signal_interval": 8,
            "min_quality_score": 0.6,
            "window": 12,
            "min_distance": 20,
            "prominence_threshold": 0.025
        }
    ]
    
    best_result = None
    best_param_name = ""
    
    for params in param_sets:
        print(f"\n--- {params['name']} ---")
        
        try:
            # 计算技术指标
            df_with_indicators = SignalDetector._calculate_all_indicators(df.copy())
            
            # 检测背离（使用自定义参数）
            divergence_results = SignalDetector.detect_multi_indicator_divergence(
                df_with_indicators,
                indicators=['rsi', 'macd_dif', 'kdj_k'],
                window=params['window'],
                min_distance=params['min_distance'],
                prominence_threshold=params['prominence_threshold']
            )
            
            # 生成交易信号
            buy_signals, sell_signals, stats = SignalDetector.generate_divergence_trading_signals(
                df_with_indicators,
                min_signal_interval=params['min_signal_interval'],
                volume_confirmation=False,  # 关闭成交量确认以获得更多信号
                min_quality_score=params['min_quality_score'],
                indicators=['rsi', 'macd_dif', 'kdj_k']
            )
            
            # 统计背离数量
            total_bullish = sum(results['bullish_divergence'].sum() for results in divergence_results.values())
            total_bearish = sum(results['bearish_divergence'].sum() for results in divergence_results.values())
            
            print(f"  背离检测: 底背离 {total_bullish}个, 顶背离 {total_bearish}个")
            print(f"  交易信号: 买入 {len(buy_signals)}个, 卖出 {len(sell_signals)}个")
            
            if len(buy_signals) > 0 or len(sell_signals) > 0:
                print(f"  平均买入质量: {stats.get('avg_buy_quality', 0):.3f}")
                print(f"  平均卖出质量: {stats.get('avg_sell_quality', 0):.3f}")
                
                # 如果有信号，记录最佳结果
                total_signals = len(buy_signals) + len(sell_signals)
                if best_result is None or total_signals > best_result['total_signals']:
                    best_result = {
                        'params': params,
                        'buy_signals': buy_signals,
                        'sell_signals': sell_signals,
                        'stats': stats,
                        'total_signals': total_signals,
                        'divergence_results': divergence_results
                    }
                    best_param_name = params['name']
            else:
                print("  [WARN] 未检测到交易信号")
                
        except Exception as e:
            print(f"  [ERROR] 参数测试失败: {e}")
    
    # 使用最佳参数生成可视化
    if best_result is not None:
        print(f"\n[INFO] 使用{best_param_name}生成可视化图表...")
        create_divergence_only_chart(fund_code, df, best_result)
        
        # 执行回测
        if best_result['total_signals'] > 0:
            print(f"\n[INFO] 执行回测验证...")
            perform_backtest(df, best_result['buy_signals'], best_result['sell_signals'])
    else:
        print(f"\n[WARN] {fund_name} 未检测到任何背离信号")

def create_divergence_only_chart(fund_code, df, result):
    """创建只显示背离信号的图表"""
    try:
        visualizer = StockEchartVisualizer()
        
        # 生成图表，只显示背离信号
        chart = visualizer.plot_stock_chart(
            fund_code,
            display_points=min(250, len(df)),
            force_refresh=False,
            detect_patterns=False,  # 关闭形态检测以简化图表
            add_trendlines=False,   # 关闭趋势线
            add_fibonacci=False,    # 关闭斐波那契线
            show_traditional_signals=False,  # 不显示传统信号
            show_divergence_signals=True     # 只显示背离信号
        )
        
        if chart is not None:
            output_file = f"cache/divergence_only_{fund_code.replace('.', '_')}.html"
            chart.render(output_file)
            print(f"  [OK] 背离信号图表保存至: {output_file}")
            print(f"  [INFO] 图表中显示 {result['total_signals']} 个背离信号")
        else:
            print("  [WARN] 图表生成失败")
            
    except Exception as e:
        print(f"  [ERROR] 可视化失败: {e}")

def perform_backtest(df, buy_signals, sell_signals):
    """执行回测"""
    try:
        backtester = DivergenceBacktester(
            initial_capital=100000,
            commission_rate=0.001,
            slippage=0.001
        )
        
        results = backtester.backtest_divergence_signals(
            df, buy_signals, sell_signals,
            holding_period=12,
            stop_loss_pct=0.06,
            take_profit_pct=0.12
        )
        
        print(f"  回测结果:")
        print(f"    总交易: {results['total_trades']} 次")
        print(f"    胜率: {results['win_rate']:.1%}")
        print(f"    总收益: {results['total_return']:.2%}")
        print(f"    最大回撤: {results['max_drawdown']:.2%}")
        print(f"    盈亏比: {results['profit_factor']:.2f}")
        
        # 分析信号质量
        quality_analysis = results.get('quality_analysis', {})
        if quality_analysis:
            print(f"    高质量信号胜率: {quality_analysis.get('high_quality_win_rate', 0):.1%}")
            print(f"    中等质量信号胜率: {quality_analysis.get('medium_quality_win_rate', 0):.1%}")
        
    except Exception as e:
        print(f"  [ERROR] 回测失败: {e}")

if __name__ == "__main__":
    main()
    print("\n" + "="*60)
    print("背离信号专项测试完成！")
    print("请查看生成的HTML文件，其中只显示背离信号")
    print("="*60)
