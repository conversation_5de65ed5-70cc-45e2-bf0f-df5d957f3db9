#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
背离交易信号系统使用示例
演示如何使用新实现的多指标背离检测和交易信号生成功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from indicator_analysis import (
    StockDataManager, SignalDetector, DivergenceBacktester, 
    StockEchartVisualizer, TechnicalIndicator
)
import pandas as pd
import numpy as np

def main():
    """主函数：演示背离交易信号系统的完整使用流程"""
    print("=" * 80)
    print("背离交易信号系统使用示例")
    print("=" * 80)
    
    # 1. 初始化数据管理器
    print("\n1. 初始化数据管理器...")
    try:
        data_manager = StockDataManager()
        print("[OK] 数据管理器初始化成功")
    except Exception as e:
        print(f"[ERROR] 数据管理器初始化失败: {e}")
        return
    
    # 2. 选择要分析的基金
    fund_code = "513260.SH"  # 恒生科技ETF
    fund_name = data_manager.get_available_funds().get(fund_code, fund_code)
    print(f"\n2. 分析基金: {fund_name} ({fund_code})")
    
    # 3. 获取历史数据
    print("\n3. 获取历史数据...")
    df = data_manager.get_fund_data(fund_code, start_date="20240101")
    if df.empty:
        print(f"[ERROR] 无法获取 {fund_code} 的数据")
        return
    print(f"[OK] 获取到 {len(df)} 条数据记录 (从 {df['trade_date'].min()} 到 {df['trade_date'].max()})")
    
    # 4. 计算技术指标
    print("\n4. 计算技术指标...")
    df_with_indicators = calculate_technical_indicators(df)
    print("[OK] 技术指标计算完成")
    
    # 5. 检测背离信号
    print("\n5. 检测多指标背离...")
    divergence_results = detect_divergences(df_with_indicators)
    
    # 6. 生成交易信号
    print("\n6. 生成背离交易信号...")
    buy_signals, sell_signals, signal_stats = generate_trading_signals(df_with_indicators)
    
    # 7. 执行回测验证
    print("\n7. 执行回测验证...")
    backtest_results = perform_backtest(df_with_indicators, buy_signals, sell_signals)
    
    # 8. 生成可视化图表
    print("\n8. 生成可视化图表...")
    create_visualization(fund_code, df_with_indicators)
    
    # 9. 输出分析报告
    print("\n9. 分析报告")
    print_analysis_report(fund_code, fund_name, signal_stats, backtest_results, divergence_results)

def calculate_technical_indicators(df):
    """计算所有需要的技术指标"""
    df_copy = df.copy()
    
    # 确保有必要的价格数据
    if 'high' not in df_copy.columns:
        df_copy['high'] = df_copy['close']
    if 'low' not in df_copy.columns:
        df_copy['low'] = df_copy['close']
    if 'open' not in df_copy.columns:
        df_copy['open'] = df_copy['close']
    
    # 计算RSI
    df_copy['rsi'] = TechnicalIndicator.calculate_rsi(df_copy['close'].values, period=14)
    
    # 计算MACD
    dif, dea, macd = TechnicalIndicator.calculate_macd(df_copy['close'].values)
    df_copy['macd_dif'] = dif
    df_copy['macd_dea'] = dea
    df_copy['macd_histogram'] = macd
    
    # 计算KDJ
    k_values, d_values, j_values = TechnicalIndicator.calculate_kdj(
        df_copy['high'].values, df_copy['low'].values, df_copy['close'].values
    )
    df_copy['kdj_k'] = k_values
    df_copy['kdj_d'] = d_values
    df_copy['kdj_j'] = j_values
    
    # 计算EMA
    df_copy['ema_short'] = TechnicalIndicator.calculate_ema(df_copy['close'].values, short_term=13)[1]
    df_copy['ema_long'] = TechnicalIndicator.calculate_ema(df_copy['close'].values, long_term=26)[0]
    
    print(f"  - RSI: {np.sum(~np.isnan(df_copy['rsi']))} 个有效值")
    print(f"  - MACD: {np.sum(~np.isnan(df_copy['macd_dif']))} 个有效值")
    print(f"  - KDJ: {np.sum(~np.isnan(df_copy['kdj_k']))} 个有效值")
    
    return df_copy

def detect_divergences(df):
    """检测多指标背离"""
    indicators = ['rsi', 'macd_dif', 'kdj_k']
    
    divergence_results = SignalDetector.detect_multi_indicator_divergence(
        df, 
        price_col='close',
        indicators=indicators,
        window=10,
        min_distance=15,
        prominence_threshold=0.02
    )
    
    print("  背离检测结果:")
    total_bullish = 0
    total_bearish = 0
    
    for indicator, results in divergence_results.items():
        bullish_count = results['bullish_divergence'].sum()
        bearish_count = results['bearish_divergence'].sum()
        total_bullish += bullish_count
        total_bearish += bearish_count
        print(f"    - {indicator.upper()}: 底背离 {bullish_count} 个, 顶背离 {bearish_count} 个")
    
    print(f"  总计: 底背离 {total_bullish} 个, 顶背离 {total_bearish} 个")
    
    return divergence_results

def generate_trading_signals(df):
    """生成背离交易信号"""
    buy_signals, sell_signals, stats = SignalDetector.generate_divergence_trading_signals(
        df,
        min_signal_interval=8,  # 信号间隔至少8天
        volume_confirmation=False,  # 暂时关闭成交量确认
        min_quality_score=0.4,  # 最低质量评分0.4
        indicators=['rsi', 'macd_dif', 'kdj_k']
    )
    
    print(f"  生成信号统计:")
    print(f"    - 买入信号: {len(buy_signals)} 个")
    print(f"    - 卖出信号: {len(sell_signals)} 个")
    print(f"    - 平均买入信号质量: {stats.get('avg_buy_quality', 0):.3f}")
    print(f"    - 平均卖出信号质量: {stats.get('avg_sell_quality', 0):.3f}")
    
    if not buy_signals.empty:
        print(f"    - 买入信号日期: {', '.join(buy_signals['trade_date'].astype(str).tolist()[:5])}{'...' if len(buy_signals) > 5 else ''}")
    
    if not sell_signals.empty:
        print(f"    - 卖出信号日期: {', '.join(sell_signals['trade_date'].astype(str).tolist()[:5])}{'...' if len(sell_signals) > 5 else ''}")
    
    return buy_signals, sell_signals, stats

def perform_backtest(df, buy_signals, sell_signals):
    """执行回测验证"""
    if buy_signals.empty and sell_signals.empty:
        print("  ⚠ 无交易信号，跳过回测")
        return {}
    
    # 初始化回测器
    backtester = DivergenceBacktester(
        initial_capital=100000,  # 10万初始资金
        commission_rate=0.0015,  # 0.15%手续费
        slippage=0.001  # 0.1%滑点
    )
    
    # 执行回测
    results = backtester.backtest_divergence_signals(
        df, buy_signals, sell_signals,
        holding_period=15,  # 最大持仓15天
        stop_loss_pct=0.08,  # 8%止损
        take_profit_pct=0.15  # 15%止盈
    )
    
    print(f"  回测结果:")
    print(f"    - 总交易次数: {results['total_trades']}")
    print(f"    - 胜率: {results['win_rate']:.1%}")
    print(f"    - 总收益率: {results['total_return']:.2%}")
    print(f"    - 年化收益率: {results['total_return'] * 4:.2%}")  # 假设数据覆盖3个月
    print(f"    - 最大回撤: {results['max_drawdown']:.2%}")
    print(f"    - 盈亏比: {results['profit_factor']:.2f}")
    print(f"    - 夏普比率: {results['sharpe_ratio']:.2f}")
    
    return results

def create_visualization(fund_code, df):
    """创建可视化图表"""
    try:
        visualizer = StockEchartVisualizer()
        
        # 生成包含背离信号的图表
        chart = visualizer.plot_stock_chart(
            fund_code,
            display_points=min(250, len(df)),
            force_refresh=False,
            detect_patterns=True,
            add_trendlines=True,
            add_fibonacci=False  # 关闭斐波那契线以简化图表
        )
        
        if chart is not None:
            output_file = f"cache/divergence_analysis_{fund_code.replace('.', '_')}.html"
            chart.render(output_file)
            print(f"  ✓ 图表已保存至: {output_file}")
            print(f"  ✓ 请在浏览器中打开查看详细的背离信号分析")
        else:
            print("  ⚠ 图表生成失败")
            
    except Exception as e:
        print(f"  ✗ 可视化失败: {e}")

def print_analysis_report(fund_code, fund_name, signal_stats, backtest_results, divergence_results):
    """输出详细的分析报告"""
    print("=" * 80)
    print(f"【{fund_name} ({fund_code}) 背离交易信号分析报告】")
    print("=" * 80)
    
    print("\n📊 信号统计:")
    print(f"  • 总买入信号: {signal_stats.get('total_buy_signals', 0)} 个")
    print(f"  • 总卖出信号: {signal_stats.get('total_sell_signals', 0)} 个")
    print(f"  • 使用指标: {', '.join(signal_stats.get('indicators_used', []))}")
    
    print("\n📈 背离分析:")
    divergence_summary = signal_stats.get('divergence_summary', {})
    for indicator, summary in divergence_summary.items():
        print(f"  • {indicator.upper()}:")
        print(f"    - 底背离: {summary.get('bullish_divergences', 0)} 个")
        print(f"    - 顶背离: {summary.get('bearish_divergences', 0)} 个")
    
    if backtest_results:
        print("\n💰 回测表现:")
        print(f"  • 交易次数: {backtest_results.get('total_trades', 0)}")
        print(f"  • 胜率: {backtest_results.get('win_rate', 0):.1%}")
        print(f"  • 总收益: {backtest_results.get('total_return', 0):.2%}")
        print(f"  • 最大回撤: {backtest_results.get('max_drawdown', 0):.2%}")
        print(f"  • 盈亏比: {backtest_results.get('profit_factor', 0):.2f}")
        
        quality_analysis = backtest_results.get('quality_analysis', {})
        if quality_analysis:
            print("\n🎯 信号质量分析:")
            print(f"  • 高质量信号(≥0.7): {quality_analysis.get('high_quality_count', 0)} 个, "
                  f"胜率 {quality_analysis.get('high_quality_win_rate', 0):.1%}")
            print(f"  • 中等质量信号(0.4-0.7): {quality_analysis.get('medium_quality_count', 0)} 个, "
                  f"胜率 {quality_analysis.get('medium_quality_win_rate', 0):.1%}")
            print(f"  • 低质量信号(<0.4): {quality_analysis.get('low_quality_count', 0)} 个, "
                  f"胜率 {quality_analysis.get('low_quality_win_rate', 0):.1%}")
    
    print("\n💡 使用建议:")
    print("  • 重点关注高质量的背离信号")
    print("  • 结合多个指标确认，避免单一指标误判")
    print("  • 设置合理的止损止盈，控制风险")
    print("  • 在趋势明确的市场中，背离信号效果更佳")
    print("  • 建议结合成交量分析，提高信号可靠性")

if __name__ == "__main__":
    main()
