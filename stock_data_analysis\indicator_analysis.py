#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
场内基金数据技术分析工具
使用Pyecharts绘制交互式图表,显示EMA和MACD等技术指标

依赖包安装:
pip install pyecharts pandas numpy scipy tushare MyTT

注意: 此版本使用Pyecharts替代了matplotlib,提供了更好的交互体验
"""

# 场内基金技术分析工具
# 提供数据获取、缓存、技术指标计算和可视化功能

import pandas as pd
import numpy as np
from MyTT import EMA  # 引入 MyTT 库中 EMA 指标计算函数
import tushare as ts
from datetime import datetime
from pathlib import Path
import configparser
import pyecharts.options as opts
from pyecharts.charts import Line, Grid, Scatter
import sqlite3
from scipy.signal import argrelextrema
import warnings

# 抑制sklearn的警告
warnings.filterwarnings("ignore", category=UserWarning)


class StockDataManager:
    """处理基金数据获取和缓存的类"""

    def __init__(self, api_key=None):
        """初始化StockDataManager

        Args:
            api_key: Tushare API密钥,如果为None则尝试从配置文件加载
        """
        self.api_key = api_key or self._load_api_key()
        if not self.api_key:
            raise ValueError("未提供API密钥,请在配置文件中设置或作为参数传入")
        script_dir = Path(__file__).parent.resolve()
        self.cache_dir = script_dir / "cache"
        self.cache_dir.mkdir(exist_ok=True)
        self.db_path = self.cache_dir / "stock_data_cache.db"
        self._init_db()
        self.today_str = datetime.now().strftime("%Y%m%d")  # 计算一次今天的日期字符串

    def _init_db(self):
        try:
            # 检查数据库文件是否被锁定
            if self.db_path.exists():
                # 尝试以读写模式打开
                self.conn = sqlite3.connect(self.db_path, timeout=10.0)
            else:
                self.conn = sqlite3.connect(self.db_path)

            # 创建基金基本信息缓存表
            self.conn.execute(
                """CREATE TABLE IF NOT EXISTS fund_cache_meta (
                ts_code TEXT NOT NULL,
                timestamp TEXT NOT NULL,
                last_update TEXT NOT NULL,
                PRIMARY KEY (ts_code, timestamp)
            )"""
            )

            # 创建基金日线数据表
            self.conn.execute(
                """CREATE TABLE IF NOT EXISTS fund_daily_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ts_code TEXT NOT NULL,
                trade_date TEXT NOT NULL,
                open REAL,
                close REAL,
                high REAL,
                low REAL,
                volume REAL,
                amount REAL,
                cache_timestamp TEXT NOT NULL,
                UNIQUE(ts_code, trade_date, cache_timestamp)
            )"""
            )

            # 创建索引以提高查询性能
            self.conn.execute(
                """CREATE INDEX IF NOT EXISTS idx_fund_daily_ts_date
                ON fund_daily_data(ts_code, trade_date)"""
            )
            self.conn.execute(
                """CREATE INDEX IF NOT EXISTS idx_fund_daily_cache_time
                ON fund_daily_data(cache_timestamp)"""
            )

            self.conn.commit()
        except sqlite3.OperationalError as e:
            print(f"数据库初始化警告: {e}")
            # 如果数据库被锁定,创建一个新的数据库文件
            backup_db_path = self.cache_dir / f"stock_data_cache_backup_{self.today_str}.db"
            print(f"尝试使用备用数据库: {backup_db_path}")
            self.db_path = backup_db_path
            self.conn = sqlite3.connect(self.db_path)

            # 重新创建表结构
            self.conn.execute(
                """CREATE TABLE IF NOT EXISTS fund_cache_meta (
                ts_code TEXT NOT NULL,
                timestamp TEXT NOT NULL,
                last_update TEXT NOT NULL,
                PRIMARY KEY (ts_code, timestamp)
            )"""
            )

            self.conn.execute(
                """CREATE TABLE IF NOT EXISTS fund_daily_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ts_code TEXT NOT NULL,
                trade_date TEXT NOT NULL,
                open REAL,
                close REAL,
                high REAL,
                low REAL,
                volume REAL,
                amount REAL,
                cache_timestamp TEXT NOT NULL,
                UNIQUE(ts_code, trade_date, cache_timestamp)
            )"""
            )

            self.conn.execute(
                """CREATE INDEX IF NOT EXISTS idx_fund_daily_ts_date
                ON fund_daily_data(ts_code, trade_date)"""
            )
            self.conn.execute(
                """CREATE INDEX IF NOT EXISTS idx_fund_daily_cache_time
                ON fund_daily_data(cache_timestamp)"""
            )

            self.conn.commit()

    def _load_api_key(self):
        """从配置文件加载API密钥"""
        config = configparser.ConfigParser()
        config_path = Path("config.ini")

        # 默认API密钥,仅作为示例
        default_key = "5aba669a485730beded0d604bd8ccfdaca39f8c9c09ef2d0ed78ed77"

        if config_path.exists():
            try:
                config.read(config_path)
                return config.get("API", "tushare_token", fallback=default_key)
            except Exception as e:
                print(f"读取配置文件失败: {str(e)}")
                return default_key
        else:
            # 创建默认配置文件
            config["API"] = {"tushare_token": default_key}
            with open(config_path, "w") as f:
                config.write(f)
            print(f"已创建默认配置文件: {config_path}")
            return default_key

    def get_cache_key(self, ts_code):
        return ts_code, self.today_str

    def is_cache_valid(self, ts_code):
        ts_code, today_str = self.get_cache_key(ts_code)
        cursor = self.conn.execute(
            "SELECT 1 FROM fund_cache_meta WHERE ts_code=? AND timestamp=?",
            (ts_code, today_str),
        )
        return cursor.fetchone() is not None

    def read_cache(self, ts_code):
        """从SQLite数据库读取缓存的基金数据"""
        ts_code, today_str = self.get_cache_key(ts_code)

        # 检查元数据是否存在
        cursor = self.conn.execute(
            "SELECT last_update FROM fund_cache_meta WHERE ts_code=? AND timestamp=?",
            (ts_code, today_str),
        )
        meta_row = cursor.fetchone()
        if not meta_row:
            return None

        # 读取日线数据
        cursor = self.conn.execute(
            """SELECT trade_date, open, close, high, low, volume, amount
               FROM fund_daily_data
               WHERE ts_code=? AND cache_timestamp=?
               ORDER BY trade_date ASC""",
            (ts_code, today_str),
        )

        rows = cursor.fetchall()
        if not rows:
            return None

        # 转换为DataFrame格式的字典
        data_records = []
        for row in rows:
            data_records.append({
                'ts_code': ts_code,
                'trade_date': row[0],
                'open': row[1],
                'close': row[2],
                'high': row[3],
                'low': row[4],
                'volume': row[5],
                'amount': row[6]
            })

        return {'timestamp': today_str, 'data': data_records}

    def write_cache(self, ts_code, cache_data):
        """将基金数据写入SQLite数据库缓存"""
        try:
            ts_code, today_str = self.get_cache_key(ts_code)

            # 开始事务
            self.conn.execute("BEGIN TRANSACTION")

            # 删除旧的缓存数据
            self.conn.execute(
                "DELETE FROM fund_daily_data WHERE ts_code=? AND cache_timestamp=?",
                (ts_code, today_str)
            )
            self.conn.execute(
                "DELETE FROM fund_cache_meta WHERE ts_code=? AND timestamp=?",
                (ts_code, today_str)
            )

            # 插入新的日线数据
            data_records = cache_data.get('data', [])
            for record in data_records:
                self.conn.execute(
                    """INSERT INTO fund_daily_data
                       (ts_code, trade_date, open, close, high, low, volume, amount, cache_timestamp)
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                    (ts_code, record['trade_date'], record['open'], record['close'],
                     record['high'], record['low'], record['volume'], record['amount'], today_str)
                )

            # 插入元数据
            self.conn.execute(
                "INSERT INTO fund_cache_meta (ts_code, timestamp, last_update) VALUES (?, ?, ?)",
                (ts_code, today_str, datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
            )

            # 提交事务
            self.conn.commit()

        except sqlite3.OperationalError as e:
            # 回滚事务
            self.conn.rollback()
            print(f"写入缓存失败: {e}")
            # 缓存写入失败不影响主要功能,只是性能会差一些

    def get_fund_data(
        self, ts_code, start_date=None, end_date=None, force_refresh=False
    ):
        today_str = datetime.now().strftime("%Y%m%d")
        if not force_refresh and self.is_cache_valid(ts_code):
            try:
                cache_data = self.read_cache(ts_code)
                if cache_data is not None:
                    df = pd.DataFrame(cache_data["data"])
                    print(f"使用缓存数据 ({ts_code})")
                    return df
                else:
                    raise ValueError("缓存数据不存在")
            except Exception as e:
                print(f"读取SQLite缓存失败: {str(e)}")
        try:
            pro = ts.pro_api(self.api_key)
            today = datetime.now().strftime("%Y%m%d")
            params = {
                "ts_code": ts_code,
                "start_date": start_date if start_date else "20240101",
                "end_date": end_date if end_date else today,
            }
            df = pro.fund_daily(
                **params, fields="ts_code,trade_date,open,close,high,low,vol,amount"
            )
            if not df.empty:
                df = df.rename(columns={"vol": "volume"})
                df = df.sort_values("trade_date", ascending=True).reset_index(drop=True)
                if end_date:
                    df = df[df["trade_date"] <= end_date]
                # type: ignore
                records_dict = df.to_dict("records")  # type: ignore
                cache_data = {"timestamp": today_str, "data": records_dict}
                self.write_cache(ts_code, cache_data)
                print(f"已更新SQLite缓存数据 ({ts_code})")
                return df
            else:
                print(f"未找到基金 {ts_code} 在指定日期区间的数据")
                return pd.DataFrame()
        except Exception as e:
            print(f"获取基金数据失败: {str(e)}")
            return pd.DataFrame()

    def get_available_funds(self):
        """获取常用的场内基金列表"""
        return {
            "501312.SH": "海外科技LOF",
            "513260.SH": "恒生科技ETF",
            "513060.SH": "恒生医疗",
            "159302.SZ": "港股高股息ETF",
            "512890.SH": "红利低波ETF",
            "513630.SH": "港股红利指数ETF",
            "515100.SH": "红利低波100ETF",
            "517900.SH": "银行ETF优选",
            "160723.SZ": "嘉实原油LOF",
            "159509.SZ": "纳指科技ETF",
            "515300.SH": "日经225ETF",
            "159985.SZ": "豆粕ETF",
        }


class TechnicalIndicator:
    """计算技术指标的类"""

    @staticmethod
    def calculate_ema(data, long_term=26, short_term=13):
        """计算长短期EMA及其差值

        Args:
            data: 收盘价数据数组
            long_term: 长期EMA周期数
            short_term: 短期EMA周期数

        Returns:
            tuple: (长期EMA, 短期EMA, 差值)
        """
        ema_long = EMA(data, long_term)
        ema_short = EMA(data, short_term)

        # 显式转换为numpy array以消除类型检查警告
        ema_difference = np.array(ema_short) - np.array(ema_long)

        return ema_long, ema_short, ema_difference

    @staticmethod
    def calculate_macd(data, fast=12, slow=26, signal=9):
        """计算MACD指标

        Args:
            data: 收盘价数据
            fast: 快线周期
            slow: 慢线周期
            signal: 信号线周期

        Returns:
            tuple: (DIF线, DEA信号线, MACD柱状图)
        """
        # 确保数据是numpy数组类型
        data = np.array(data)

        # 计算快线和慢线EMA
        ema_fast = EMA(data, fast)
        ema_slow = EMA(data, slow)

        # 计算DIF
        dif = np.array(ema_fast) - np.array(ema_slow)

        # 计算DEA信号线
        dea = EMA(dif, signal)

        # 计算MACD柱状图
        macd = (np.array(dif) - np.array(dea)) * 2

        return dif, dea, macd

    @staticmethod
    def smooth_data(data, window_size=None):
        """使用EWMA(指数加权移动平均)平滑处理数据

        Args:
            data: 需要平滑的数据数组
            window_size: 窗口大小,如果为None则自动计算

        Returns:
            平滑后的数据
        """
        data_len = len(data)

        # 如果数据长度不足,返回原始数据
        if data_len < 5:
            return data

        # 自动计算窗口大小(如果未指定)
        if window_size is None:
            window_size = min(int(data_len * 0.15), 51)
            # 确保窗口大小为奇数
            if window_size % 2 == 0:
                window_size += 1

        # 确保窗口大小有效
        window_size = max(3, window_size)

        # 指数加权移动平均
        alpha = 2 / (window_size + 1)  # 平滑因子
        return pd.Series(data).ewm(alpha=alpha, adjust=False).mean().values

    @staticmethod
    def calculate_rsi(data, period=14):
        """计算RSI相对强弱指标

        Args:
            data: 收盘价数据
            period: RSI计算周期,默认14

        Returns:
            numpy.array: RSI值数组
        """
        data = np.array(data)
        if len(data) < period + 1:
            return np.full(len(data), np.nan)

        # 计算价格变化
        delta = np.diff(data)

        # 分离上涨和下跌
        gains = np.where(delta > 0, delta, 0)
        losses = np.where(delta < 0, -delta, 0)

        # 计算初始平均收益和损失
        avg_gain = np.mean(gains[:period])
        avg_loss = np.mean(losses[:period])

        # 初始化RSI数组
        rsi = np.full(len(data), np.nan)

        # 计算第一个RSI值
        if avg_loss != 0:
            rs = avg_gain / avg_loss
            rsi[period] = 100 - (100 / (1 + rs))
        else:
            rsi[period] = 100

        # 使用Wilder's smoothing计算后续RSI值
        for i in range(period + 1, len(data)):
            gain = gains[i-1] if i-1 < len(gains) else 0
            loss = losses[i-1] if i-1 < len(losses) else 0

            avg_gain = (avg_gain * (period - 1) + gain) / period
            avg_loss = (avg_loss * (period - 1) + loss) / period

            if avg_loss != 0:
                rs = avg_gain / avg_loss
                rsi[i] = 100 - (100 / (1 + rs))
            else:
                rsi[i] = 100

        return rsi

    @staticmethod
    def calculate_kdj(high, low, close, k_period=9, d_period=3, j_period=3):
        """计算KDJ随机指标

        Args:
            high: 最高价数据
            low: 最低价数据
            close: 收盘价数据
            k_period: K值计算周期,默认9
            d_period: D值平滑周期,默认3
            j_period: J值计算周期,默认3

        Returns:
            tuple: (K值, D值, J值)
        """
        high = np.array(high)
        low = np.array(low)
        close = np.array(close)

        if len(high) < k_period:
            return (np.full(len(high), np.nan),
                   np.full(len(high), np.nan),
                   np.full(len(high), np.nan))

        # 计算RSV (Raw Stochastic Value)
        rsv = np.full(len(close), np.nan)

        for i in range(k_period - 1, len(close)):
            period_high = np.max(high[i - k_period + 1:i + 1])
            period_low = np.min(low[i - k_period + 1:i + 1])

            if period_high != period_low:
                rsv[i] = (close[i] - period_low) / (period_high - period_low) * 100
            else:
                rsv[i] = 50  # 当最高价等于最低价时,设为50

        # 计算K值 (使用指数移动平均)
        k_values = np.full(len(close), np.nan)
        k_values[k_period - 1] = rsv[k_period - 1] if not np.isnan(rsv[k_period - 1]) else 50

        for i in range(k_period, len(close)):
            if not np.isnan(rsv[i]):
                k_values[i] = (2/3) * k_values[i-1] + (1/3) * rsv[i]
            else:
                k_values[i] = k_values[i-1]

        # 计算D值 (K值的指数移动平均)
        d_values = np.full(len(close), np.nan)
        d_values[k_period - 1] = k_values[k_period - 1]

        for i in range(k_period, len(close)):
            if not np.isnan(k_values[i]):
                d_values[i] = (2/3) * d_values[i-1] + (1/3) * k_values[i]
            else:
                d_values[i] = d_values[i-1]

        # 计算J值
        j_values = 3 * k_values - 2 * d_values

        return k_values, d_values, j_values

    @staticmethod
    def detect_peaks_and_troughs(data, window=5, min_distance=10, prominence_threshold=0.02):
        """增强的峰谷检测算法

        Args:
            data: 价格或指标数据
            window: 局部极值检测窗口大小
            min_distance: 峰谷之间的最小距离
            prominence_threshold: 峰谷显著性阈值(相对于数据范围的比例)

        Returns:
            tuple: (峰值索引数组, 谷值索引数组, 峰值信息, 谷值信息)
        """
        data = np.array(data)
        if len(data) < window * 2:
            return np.array([]), np.array([]), [], []

        # 使用scipy的argrelextrema进行初步检测
        peak_candidates = argrelextrema(data, np.greater, order=window)[0]
        trough_candidates = argrelextrema(data, np.less, order=window)[0]

        # 计算数据范围用于显著性过滤
        data_range = np.max(data) - np.min(data)
        min_prominence = data_range * prominence_threshold

        # 过滤峰值:检查显著性和最小距离
        filtered_peaks = []
        peak_info = []

        for peak_idx in peak_candidates:
            # 检查显著性:峰值应该明显高于周围的点
            left_min = np.min(data[max(0, peak_idx - window):peak_idx])
            right_min = np.min(data[peak_idx + 1:min(len(data), peak_idx + window + 1)])
            prominence = data[peak_idx] - max(left_min, right_min)

            if prominence >= min_prominence:
                # 检查与已有峰值的距离
                if not filtered_peaks or min(abs(peak_idx - p) for p in filtered_peaks) >= min_distance:
                    filtered_peaks.append(peak_idx)
                    peak_info.append({
                        'index': peak_idx,
                        'value': data[peak_idx],
                        'prominence': prominence,
                        'left_base': left_min,
                        'right_base': right_min
                    })

        # 过滤谷值:检查显著性和最小距离
        filtered_troughs = []
        trough_info = []

        for trough_idx in trough_candidates:
            # 检查显著性:谷值应该明显低于周围的点
            left_max = np.max(data[max(0, trough_idx - window):trough_idx])
            right_max = np.max(data[trough_idx + 1:min(len(data), trough_idx + window + 1)])
            prominence = min(left_max, right_max) - data[trough_idx]

            if prominence >= min_prominence:
                # 检查与已有谷值的距离
                if not filtered_troughs or min(abs(trough_idx - t) for t in filtered_troughs) >= min_distance:
                    filtered_troughs.append(trough_idx)
                    trough_info.append({
                        'index': trough_idx,
                        'value': data[trough_idx],
                        'prominence': prominence,
                        'left_base': left_max,
                        'right_base': right_max
                    })

        return np.array(filtered_peaks), np.array(filtered_troughs), peak_info, trough_info

    @staticmethod
    def find_corresponding_extrema(price_extrema, indicator_extrema, max_distance=10):
        """寻找价格极值点对应的指标极值点

        Args:
            price_extrema: 价格极值点索引数组
            indicator_extrema: 指标极值点索引数组
            max_distance: 最大匹配距离

        Returns:
            list: 匹配的极值点对列表 [(price_idx, indicator_idx), ...]
        """
        matches = []

        for price_idx in price_extrema:
            # 寻找最近的指标极值点
            distances = np.abs(indicator_extrema - price_idx)
            if len(distances) > 0:
                min_distance_idx = np.argmin(distances)
                min_distance = distances[min_distance_idx]

                if min_distance <= max_distance:
                    indicator_idx = indicator_extrema[min_distance_idx]
                    matches.append((price_idx, indicator_idx))

        return matches

    @staticmethod
    def calculate_fibonacci_levels(high, low):
        """计算斐波那契回调和延伸水平

        Args:
            high: 高点价格
            low: 低点价格

        Returns:
            dict: 斐波那契回调和延伸水平
        """
        diff = high - low

        # 常用斐波那契回调比例: 0.236, 0.382, 0.5, 0.618, 0.786
        # 常用斐波那契延伸比例: 1.272, 1.618, 2.618, 4.236
        levels = {
            "回调_0.236": high - 0.236 * diff,
            "回调_0.382": high - 0.382 * diff,
            "回调_0.5": high - 0.5 * diff,
            "回调_0.618": high - 0.618 * diff,
            "回调_0.786": high - 0.786 * diff,
            "延伸_1.272": low + 1.272 * diff,
            "延伸_1.618": low + 1.618 * diff,
            "延伸_2.618": low + 2.618 * diff,
            "延伸_4.236": low + 4.236 * diff,
        }

        return levels

    @staticmethod
    def detect_head_shoulders(prices, window_size=20, height_threshold=0.02):
        """检测头肩顶/底形态

        Args:
            prices: 价格序列
            window_size: 用于寻找峰值的窗口大小
            height_threshold: 高度阈值,头部与肩部高度差的最小比例

        Returns:
            tuple: (头肩顶位置列表, 头肩底位置列表)
        """
        # 确保有足够的数据点
        if len(prices) < window_size * 3:
            return [], []

        # 寻找局部极大值点和极小值点
        maxima = argrelextrema(np.array(prices), np.greater, order=window_size)[0]
        minima = argrelextrema(np.array(prices), np.less, order=window_size)[0]

        if len(maxima) < 3 or len(minima) < 2:
            return [], []

        # 头肩顶形态检测 (三个峰,中间峰最高)
        head_shoulders_top = []
        for i in range(len(maxima) - 2):
            left_shoulder = maxima[i]
            head = maxima[i + 1]
            right_shoulder = maxima[i + 2]

            # 检查峰值顺序
            if not (left_shoulder < head < right_shoulder):
                continue

            # 检查头部必须高于两肩
            if not (
                prices[head] > prices[left_shoulder]
                and prices[head] > prices[right_shoulder]
            ):
                continue

            # 检查两肩高度相近 (相差不超过20%)
            shoulder_diff = (
                abs(prices[left_shoulder] - prices[right_shoulder])
                / prices[left_shoulder]
            )
            if shoulder_diff > 0.2:
                continue

            # 检查头部比肩部高出足够量
            height_diff_left = (prices[head] - prices[left_shoulder]) / prices[
                left_shoulder
            ]
            height_diff_right = (prices[head] - prices[right_shoulder]) / prices[
                right_shoulder
            ]
            if (
                height_diff_left < height_threshold
                or height_diff_right < height_threshold
            ):
                continue

            # 寻找颈线 (两肩之间的低点)
            neck_candidates = [
                idx for idx in minima if left_shoulder < idx < right_shoulder
            ]
            if not neck_candidates:
                continue

            neck = neck_candidates[np.argmin([prices[idx] for idx in neck_candidates])]

            # 检查右肩后是否有低于颈线的点 (形成突破)
            breakthrough = False
            for j in range(
                right_shoulder + 1, min(right_shoulder + window_size, len(prices))
            ):
                if j < len(prices) and prices[j] < prices[neck]:
                    breakthrough = True
                    break

            if breakthrough:
                head_shoulders_top.append((left_shoulder, head, right_shoulder, neck))

        # 头肩底形态检测 (三个谷,中间谷最低)
        head_shoulders_bottom = []
        for i in range(len(minima) - 2):
            left_shoulder = minima[i]
            head = minima[i + 1]
            right_shoulder = minima[i + 2]

            # 检查谷值顺序
            if not (left_shoulder < head < right_shoulder):
                continue

            # 检查头部必须低于两肩
            if not (
                prices[head] < prices[left_shoulder]
                and prices[head] < prices[right_shoulder]
            ):
                continue

            # 检查两肩高度相近 (相差不超过20%)
            shoulder_diff = (
                abs(prices[left_shoulder] - prices[right_shoulder])
                / prices[left_shoulder]
            )
            if shoulder_diff > 0.2:
                continue

            # 检查头部比肩部低出足够量
            height_diff_left = (prices[left_shoulder] - prices[head]) / prices[head]
            height_diff_right = (prices[right_shoulder] - prices[head]) / prices[head]
            if (
                height_diff_left < height_threshold
                or height_diff_right < height_threshold
            ):
                continue

            # 寻找颈线 (两肩之间的高点)
            neck_candidates = [
                idx for idx in maxima if left_shoulder < idx < right_shoulder
            ]
            if not neck_candidates:
                continue

            neck = neck_candidates[np.argmax([prices[idx] for idx in neck_candidates])]

            # 检查右肩后是否有高于颈线的点 (形成突破)
            breakthrough = False
            for j in range(
                right_shoulder + 1, min(right_shoulder + window_size, len(prices))
            ):
                if j < len(prices) and prices[j] > prices[neck]:
                    breakthrough = True
                    break

            if breakthrough:
                head_shoulders_bottom.append(
                    (left_shoulder, head, right_shoulder, neck)
                )

        return head_shoulders_top, head_shoulders_bottom

    @staticmethod
    def detect_double_top_bottom(
        prices, window_size=20, height_threshold=0.02, similarity_threshold=0.03
    ):
        """检测双顶/双底形态 - 优化版

        Args:
            prices: 价格序列
            window_size: 用于寻找峰值的窗口大小
            height_threshold: 高度阈值,顶部或底部与中间点高度差的最小比例
            similarity_threshold: 两个顶/底相似度阈值

        Returns:
            tuple: (双顶位置列表, 双底位置列表)
        """
        # 确保有足够的数据点
        if len(prices) < window_size * 2:
            return [], []

        # 寻找局部极大值点和极小值点
        maxima = argrelextrema(np.array(prices), np.greater, order=window_size)[0]
        minima = argrelextrema(np.array(prices), np.less, order=window_size)[0]

        if len(maxima) < 2 or len(minima) < 1:
            return [], []

        # 双顶形态检测 - 增强版
        double_tops = []
        for i in range(len(maxima) - 1):
            first_top = maxima[i]
            second_top = maxima[i + 1]

            # 检查两个顶点间距 - 更严格的条件
            if second_top - first_top < window_size * 1.5:
                continue

            # 检查两个顶点高度相似 - 更精确的计算
            avg_price = (prices[first_top] + prices[second_top]) / 2
            price_diff = abs(prices[first_top] - prices[second_top]) / avg_price
            if price_diff > similarity_threshold:
                continue

            # 寻找两顶之间的低点
            neck_candidates = [idx for idx in minima if first_top < idx < second_top]
            if not neck_candidates:
                continue

            neck = neck_candidates[np.argmin([prices[idx] for idx in neck_candidates])]

            # 检查颈线与顶点高度差 - 更严格的验证
            height_diff1 = (prices[first_top] - prices[neck]) / prices[neck]
            height_diff2 = (prices[second_top] - prices[neck]) / prices[neck]
            if height_diff1 < height_threshold or height_diff2 < height_threshold:
                continue

            # 检查成交量确认(如果有成交量数据)
            volume_confirmed = True  # 默认确认,如果没有成交量数据

            # 检查第二个顶点后是否有低于颈线的点 (形成突破)
            breakthrough = False
            for j in range(second_top + 1, min(second_top + window_size, len(prices))):
                if j < len(prices) and prices[j] < prices[neck] * 0.98:  # 增加2%的突破确认
                    breakthrough = True
                    break

            if breakthrough and volume_confirmed:
                confidence = 1.0 - price_diff  # 相似度越高,置信度越高
                double_tops.append((first_top, second_top, neck, confidence))

        # 双底形态检测 - 增强版
        double_bottoms = []
        for i in range(len(minima) - 1):
            first_bottom = minima[i]
            second_bottom = minima[i + 1]

            # 检查两个底点间距
            if second_bottom - first_bottom < window_size * 1.5:
                continue

            # 检查两个底点高度相似
            avg_price = (prices[first_bottom] + prices[second_bottom]) / 2
            price_diff = abs(prices[first_bottom] - prices[second_bottom]) / avg_price
            if price_diff > similarity_threshold:
                continue

            # 寻找两底之间的高点
            neck_candidates = [
                idx for idx in maxima if first_bottom < idx < second_bottom
            ]
            if not neck_candidates:
                continue

            neck = neck_candidates[np.argmax([prices[idx] for idx in neck_candidates])]

            # 检查颈线与底点高度差
            height_diff1 = (prices[neck] - prices[first_bottom]) / prices[first_bottom]
            height_diff2 = (prices[neck] - prices[second_bottom]) / prices[second_bottom]
            if height_diff1 < height_threshold or height_diff2 < height_threshold:
                continue

            # 检查第二个底点后是否有高于颈线的点 (形成突破)
            breakthrough = False
            for j in range(
                second_bottom + 1, min(second_bottom + window_size, len(prices))
            ):
                if j < len(prices) and prices[j] > prices[neck] * 1.02:  # 增加2%的突破确认
                    breakthrough = True
                    break

            if breakthrough:
                confidence = 1.0 - price_diff
                double_bottoms.append((first_bottom, second_bottom, neck, confidence))

        return double_tops, double_bottoms

    @staticmethod
    def detect_triangle_patterns(prices, window_size=20, min_touches=3):
        """检测三角形形态(上升三角形、下降三角形、对称三角形)

        Args:
            prices: 价格序列
            window_size: 用于寻找峰值的窗口大小
            min_touches: 最少触及次数

        Returns:
            tuple: (上升三角形, 下降三角形, 对称三角形)
        """
        if len(prices) < window_size * 4:
            return [], [], []

        maxima = argrelextrema(np.array(prices), np.greater, order=window_size)[0]
        minima = argrelextrema(np.array(prices), np.less, order=window_size)[0]

        ascending_triangles = []
        descending_triangles = []
        symmetric_triangles = []

        # 上升三角形:水平阻力线 + 上升支撑线
        if len(maxima) >= min_touches and len(minima) >= min_touches:
            for i in range(len(maxima) - min_touches + 1):
                resistance_points = maxima[i:i + min_touches]
                resistance_prices = [prices[idx] for idx in resistance_points]

                # 检查阻力线是否水平(变化小于2%)
                resistance_range = (max(resistance_prices) - min(resistance_prices)) / np.mean(resistance_prices)
                if resistance_range < 0.02:
                    # 寻找对应的支撑点
                    support_candidates = [idx for idx in minima if resistance_points[0] < idx < resistance_points[-1]]
                    if len(support_candidates) >= 2:
                        # 检查支撑线是否上升
                        support_slope = (prices[support_candidates[-1]] - prices[support_candidates[0]]) / (support_candidates[-1] - support_candidates[0])
                        if support_slope > 0:
                            ascending_triangles.append({
                                'resistance_points': resistance_points,
                                'support_points': support_candidates,
                                'pattern_type': 'ascending_triangle'
                            })

        # 下降三角形:下降阻力线 + 水平支撑线
        if len(minima) >= min_touches and len(maxima) >= min_touches:
            for i in range(len(minima) - min_touches + 1):
                support_points = minima[i:i + min_touches]
                support_prices = [prices[idx] for idx in support_points]

                # 检查支撑线是否水平
                support_range = (max(support_prices) - min(support_prices)) / np.mean(support_prices)
                if support_range < 0.02:
                    # 寻找对应的阻力点
                    resistance_candidates = [idx for idx in maxima if support_points[0] < idx < support_points[-1]]
                    if len(resistance_candidates) >= 2:
                        # 检查阻力线是否下降
                        resistance_slope = (prices[resistance_candidates[-1]] - prices[resistance_candidates[0]]) / (resistance_candidates[-1] - resistance_candidates[0])
                        if resistance_slope < 0:
                            descending_triangles.append({
                                'support_points': support_points,
                                'resistance_points': resistance_candidates,
                                'pattern_type': 'descending_triangle'
                            })

        # 对称三角形:下降阻力线 + 上升支撑线
        if len(maxima) >= 2 and len(minima) >= 2:
            for i in range(len(maxima) - 1):
                for j in range(len(minima) - 1):
                    resistance_points = maxima[i:i + 2]
                    support_points = minima[j:j + 2]

                    # 确保时间顺序正确
                    if resistance_points[0] < support_points[-1] and support_points[0] < resistance_points[-1]:
                        resistance_slope = (prices[resistance_points[1]] - prices[resistance_points[0]]) / (resistance_points[1] - resistance_points[0])
                        support_slope = (prices[support_points[1]] - prices[support_points[0]]) / (support_points[1] - support_points[0])

                        # 阻力线下降,支撑线上升
                        if resistance_slope < -0.001 and support_slope > 0.001:
                            symmetric_triangles.append({
                                'resistance_points': resistance_points,
                                'support_points': support_points,
                                'pattern_type': 'symmetric_triangle'
                            })

        return ascending_triangles, descending_triangles, symmetric_triangles

    @staticmethod
    def detect_wedge_patterns(prices, window_size=20, min_length=30):
        """检测楔形形态(上升楔形、下降楔形)

        Args:
            prices: 价格序列
            window_size: 用于寻找峰值的窗口大小
            min_length: 楔形的最小长度

        Returns:
            tuple: (上升楔形, 下降楔形)
        """
        if len(prices) < min_length:
            return [], []

        maxima = argrelextrema(np.array(prices), np.greater, order=window_size)[0]
        minima = argrelextrema(np.array(prices), np.less, order=window_size)[0]

        rising_wedges = []
        falling_wedges = []

        # 上升楔形:两条趋势线都向上,但上轨线斜率更陡
        if len(maxima) >= 2 and len(minima) >= 2:
            for i in range(len(maxima) - 1):
                for j in range(len(minima) - 1):
                    resistance_points = maxima[i:i + 2]
                    support_points = minima[j:j + 2]

                    # 确保楔形长度足够
                    if max(resistance_points[-1], support_points[-1]) - min(resistance_points[0], support_points[0]) < min_length:
                        continue

                    resistance_slope = (prices[resistance_points[1]] - prices[resistance_points[0]]) / (resistance_points[1] - resistance_points[0])
                    support_slope = (prices[support_points[1]] - prices[support_points[0]]) / (support_points[1] - support_points[0])

                    # 上升楔形:两线都上升,但阻力线斜率更大
                    if resistance_slope > 0 and support_slope > 0 and resistance_slope > support_slope * 1.2:
                        rising_wedges.append({
                            'resistance_points': resistance_points,
                            'support_points': support_points,
                            'pattern_type': 'rising_wedge'
                        })

        # 下降楔形:两条趋势线都向下,但下轨线斜率更陡
        if len(maxima) >= 2 and len(minima) >= 2:
            for i in range(len(maxima) - 1):
                for j in range(len(minima) - 1):
                    resistance_points = maxima[i:i + 2]
                    support_points = minima[j:j + 2]

                    if max(resistance_points[-1], support_points[-1]) - min(resistance_points[0], support_points[0]) < min_length:
                        continue

                    resistance_slope = (prices[resistance_points[1]] - prices[resistance_points[0]]) / (resistance_points[1] - resistance_points[0])
                    support_slope = (prices[support_points[1]] - prices[support_points[0]]) / (support_points[1] - support_points[0])

                    # 下降楔形:两线都下降,但支撑线斜率更大(更负)
                    if resistance_slope < 0 and support_slope < 0 and support_slope < resistance_slope * 1.2:
                        falling_wedges.append({
                            'resistance_points': resistance_points,
                            'support_points': support_points,
                            'pattern_type': 'falling_wedge'
                        })

        return rising_wedges, falling_wedges

    @staticmethod
    def detect_rectangle_patterns(prices, window_size=20, min_touches=3, tolerance=0.02):
        """检测矩形整理形态

        Args:
            prices: 价格序列
            window_size: 用于寻找峰值的窗口大小
            min_touches: 最少触及次数
            tolerance: 水平线的容差

        Returns:
            list: 矩形形态列表
        """
        if len(prices) < window_size * 4:
            return []

        maxima = argrelextrema(np.array(prices), np.greater, order=window_size)[0]
        minima = argrelextrema(np.array(prices), np.less, order=window_size)[0]

        rectangles = []

        # 寻找水平阻力线和支撑线
        if len(maxima) >= min_touches and len(minima) >= min_touches:
            for i in range(len(maxima) - min_touches + 1):
                resistance_points = maxima[i:i + min_touches]
                resistance_prices = [prices[idx] for idx in resistance_points]
                resistance_avg = np.mean(resistance_prices)

                # 检查阻力线是否水平
                resistance_range = (max(resistance_prices) - min(resistance_prices)) / resistance_avg
                if resistance_range <= tolerance:
                    # 寻找对应的支撑线
                    for j in range(len(minima) - min_touches + 1):
                        support_points = minima[j:j + min_touches]
                        support_prices = [prices[idx] for idx in support_points]
                        support_avg = np.mean(support_prices)

                        # 检查支撑线是否水平
                        support_range = (max(support_prices) - min(support_prices)) / support_avg
                        if support_range <= tolerance:
                            # 检查阻力线和支撑线是否在合理的时间范围内
                            time_overlap = (min(resistance_points[-1], support_points[-1]) -
                                          max(resistance_points[0], support_points[0]))
                            if time_overlap > window_size:
                                # 检查矩形的高度是否合理
                                rectangle_height = (resistance_avg - support_avg) / support_avg
                                if 0.03 <= rectangle_height <= 0.15:  # 3%-15%的高度范围
                                    rectangles.append({
                                        'resistance_points': resistance_points,
                                        'support_points': support_points,
                                        'resistance_level': resistance_avg,
                                        'support_level': support_avg,
                                        'pattern_type': 'rectangle',
                                        'height_ratio': rectangle_height
                                    })

        return rectangles


class SignalDetector:
    """优化的信号检测器 - 多重确认机制"""

    @staticmethod
    def detect_enhanced_signals(df, ema_short_col='ema_short', ema_long_col='ema_long',
                               momentum_col='momentum', volume_col='volume'):
        """
        检测增强版买卖信号,使用多重确认机制

        Args:
            df: 包含价格和技术指标的DataFrame
            ema_short_col: 短期EMA列名
            ema_long_col: 长期EMA列名
            momentum_col: 动量指标列名
            volume_col: 成交量列名

        Returns:
            tuple: (买入信号DataFrame, 卖出信号DataFrame, 信号质量评分)
        """
        if len(df) < 50:
            return pd.DataFrame(), pd.DataFrame(), {}

        # 1. 基础信号:动量金叉死叉
        momentum_crossover = (df[momentum_col].shift(1) <= 0) & (df[momentum_col] > 0)
        momentum_crossunder = (df[momentum_col].shift(1) >= 0) & (df[momentum_col] < 0)

        # 2. EMA趋势确认
        ema_bullish = df[ema_short_col] > df[ema_long_col]
        ema_bearish = df[ema_short_col] < df[ema_long_col]

        # 3. 价格位置确认(相对于EMA的位置)
        price_above_ema_short = df['close'] > df[ema_short_col]
        price_below_ema_short = df['close'] < df[ema_short_col]

        # 4. 成交量确认(如果有成交量数据)
        volume_confirmed_buy = pd.Series([True] * len(df), index=df.index)
        volume_confirmed_sell = pd.Series([True] * len(df), index=df.index)

        if volume_col in df.columns and not df[volume_col].isna().all():
            volume_ma = df[volume_col].rolling(window=10).mean()
            volume_confirmed_buy = df[volume_col] > volume_ma * 1.2  # 成交量放大20%
            volume_confirmed_sell = df[volume_col] > volume_ma * 1.1  # 卖出信号成交量要求稍低

        # 5. 动量强度确认
        momentum_strength_buy = df[momentum_col] > df[momentum_col].rolling(window=5).mean() * 1.5
        momentum_strength_sell = df[momentum_col] < df[momentum_col].rolling(window=5).mean() * 1.5

        # 6. 价格动量确认(价格变化率)
        price_change = df['close'].pct_change(periods=3)
        price_momentum_buy = price_change > 0.01  # 3日涨幅超过1%
        price_momentum_sell = price_change < -0.01  # 3日跌幅超过1%

        # 7. 综合买入信号(多重确认)
        buy_signals = (
            momentum_crossover &  # 动量金叉
            ema_bullish &  # EMA多头排列
            price_above_ema_short &  # 价格在短期EMA之上
            volume_confirmed_buy &  # 成交量确认
            momentum_strength_buy &  # 动量强度确认
            price_momentum_buy  # 价格动量确认
        )

        # 8. 综合卖出信号(多重确认)
        sell_signals = (
            momentum_crossunder &  # 动量死叉
            ema_bearish &  # EMA空头排列
            price_below_ema_short &  # 价格在短期EMA之下
            volume_confirmed_sell &  # 成交量确认
            momentum_strength_sell &  # 动量强度确认
            price_momentum_sell  # 价格动量确认
        )

        # 9. 信号过滤:避免过于频繁的信号
        buy_signals = SignalDetector._filter_frequent_signals(buy_signals, min_interval=5)
        sell_signals = SignalDetector._filter_frequent_signals(sell_signals, min_interval=5)

        # 10. 计算信号质量评分
        buy_quality = SignalDetector._calculate_signal_quality(df, buy_signals, 'buy')
        sell_quality = SignalDetector._calculate_signal_quality(df, sell_signals, 'sell')

        # 11. 生成信号DataFrame
        buy_df = df[buy_signals].copy()
        sell_df = df[sell_signals].copy()

        if not buy_df.empty:
            buy_df['signal_type'] = 'buy'
            buy_df['quality_score'] = buy_quality

        if not sell_df.empty:
            sell_df['signal_type'] = 'sell'
            sell_df['quality_score'] = sell_quality

        quality_stats = {
            'buy_signal_count': len(buy_df),
            'sell_signal_count': len(sell_df),
            'avg_buy_quality': buy_quality.mean() if len(buy_quality) > 0 else 0,
            'avg_sell_quality': sell_quality.mean() if len(sell_quality) > 0 else 0
        }

        return buy_df, sell_df, quality_stats

    @staticmethod
    def _filter_frequent_signals(signals, min_interval=5):
        """过滤过于频繁的信号"""
        if signals.sum() == 0:
            return signals

        filtered_signals = signals.copy()
        signal_indices = signals[signals].index.tolist()

        # 移除间隔过近的信号
        for i in range(1, len(signal_indices)):
            if signal_indices[i] - signal_indices[i-1] < min_interval:
                filtered_signals.loc[signal_indices[i]] = False

        return filtered_signals

    @staticmethod
    def _calculate_signal_quality(df, signals, signal_type):
        """计算信号质量评分"""
        if signals.sum() == 0:
            return pd.Series([], dtype=float)

        quality_scores = []
        signal_indices = signals[signals].index.tolist()

        for idx in signal_indices:
            score = 0.5  # 基础分数

            # 根据动量强度调整分数
            if 'momentum' in df.columns:
                momentum_val = df.loc[idx, 'momentum']
                if signal_type == 'buy' and momentum_val > 0:
                    score += min(0.3, momentum_val * 10)  # 动量越强,分数越高
                elif signal_type == 'sell' and momentum_val < 0:
                    score += min(0.3, abs(momentum_val) * 10)

            # 根据EMA差值调整分数
            if 'ema_diff' in df.columns:
                ema_diff = df.loc[idx, 'ema_diff']
                if signal_type == 'buy' and ema_diff > 0:
                    score += min(0.2, ema_diff * 5)
                elif signal_type == 'sell' and ema_diff < 0:
                    score += min(0.2, abs(ema_diff) * 5)

            # 根据价格位置调整分数
            if 'close' in df.columns and 'ema_short' in df.columns:
                price_position = (df.loc[idx, 'close'] - df.loc[idx, 'ema_short']) / df.loc[idx, 'ema_short']
                if signal_type == 'buy' and price_position > 0:
                    score += min(0.2, price_position * 2)
                elif signal_type == 'sell' and price_position < 0:
                    score += min(0.2, abs(price_position) * 2)

            quality_scores.append(min(1.0, score))  # 限制最高分数为1.0

        return pd.Series(quality_scores, index=signal_indices)

    @staticmethod
    def detect_divergence_signals(df, price_col='close', momentum_col='momentum', window=20):
        """
        检测背离信号

        Args:
            df: 数据DataFrame
            price_col: 价格列名
            momentum_col: 动量指标列名
            window: 检测窗口大小

        Returns:
            tuple: (顶背离信号, 底背离信号)
        """
        if len(df) < window * 2:
            return pd.Series([False] * len(df), index=df.index), pd.Series([False] * len(df), index=df.index)

        # 寻找价格和动量的局部极值
        price_maxima = argrelextrema(df[price_col].values, np.greater, order=window//2)[0]
        price_minima = argrelextrema(df[price_col].values, np.less, order=window//2)[0]
        momentum_maxima = argrelextrema(df[momentum_col].values, np.greater, order=window//2)[0]
        momentum_minima = argrelextrema(df[momentum_col].values, np.less, order=window//2)[0]

        bearish_divergence = pd.Series([False] * len(df), index=df.index)
        bullish_divergence = pd.Series([False] * len(df), index=df.index)

        # 检测顶背离(价格创新高,动量不创新高)
        for i in range(1, len(price_maxima)):
            current_price_peak = price_maxima[i]
            prev_price_peak = price_maxima[i-1]

            # 寻找对应的动量峰值
            momentum_peaks_in_range = [m for m in momentum_maxima
                                     if prev_price_peak <= m <= current_price_peak]

            if len(momentum_peaks_in_range) >= 2:
                current_momentum_peak = momentum_peaks_in_range[-1]
                prev_momentum_peak = momentum_peaks_in_range[-2]

                # 价格创新高但动量未创新高
                if (df.iloc[current_price_peak][price_col] > df.iloc[prev_price_peak][price_col] and
                    df.iloc[current_momentum_peak][momentum_col] < df.iloc[prev_momentum_peak][momentum_col]):
                    bearish_divergence.iloc[current_price_peak] = True

        # 检测底背离(价格创新低,动量不创新低)
        for i in range(1, len(price_minima)):
            current_price_trough = price_minima[i]
            prev_price_trough = price_minima[i-1]

            # 寻找对应的动量谷值
            momentum_troughs_in_range = [m for m in momentum_minima
                                       if prev_price_trough <= m <= current_price_trough]

            if len(momentum_troughs_in_range) >= 2:
                current_momentum_trough = momentum_troughs_in_range[-1]
                prev_momentum_trough = momentum_troughs_in_range[-2]

                # 价格创新低但动量未创新低
                if (df.iloc[current_price_trough][price_col] < df.iloc[prev_price_trough][price_col] and
                    df.iloc[current_momentum_trough][momentum_col] > df.iloc[prev_momentum_trough][momentum_col]):
                    bullish_divergence.iloc[current_price_trough] = True

        return bearish_divergence, bullish_divergence

    @staticmethod
    def detect_multi_indicator_divergence(df, price_col='close', indicators=['rsi', 'macd_dif', 'kdj_k'],
                                        window=10, min_distance=15, prominence_threshold=0.02):
        """多指标背离检测系统

        Args:
            df: 包含价格和技术指标的DataFrame
            price_col: 价格列名
            indicators: 要检测背离的指标列名列表
            window: 峰谷检测窗口大小
            min_distance: 峰谷之间最小距离
            prominence_threshold: 显著性阈值

        Returns:
            dict: 包含各指标背离信号的字典
        """
        if len(df) < window * 4:
            return {}

        results = {}

        # 检测价格的峰谷
        price_data = df[price_col].values
        price_peaks, price_troughs, _, _ = TechnicalIndicator.detect_peaks_and_troughs(
            price_data, window=window, min_distance=min_distance, prominence_threshold=prominence_threshold
        )

        # 对每个指标检测背离
        for indicator in indicators:
            if indicator not in df.columns:
                continue

            indicator_data = df[indicator].values

            # 检测指标的峰谷
            indicator_peaks, indicator_troughs, _, _ = TechnicalIndicator.detect_peaks_and_troughs(
                indicator_data, window=window, min_distance=min_distance, prominence_threshold=prominence_threshold
            )

            # 检测顶背离(价格创新高,指标不创新高)
            bearish_divergences = SignalDetector._detect_bearish_divergence(
                df, price_peaks, indicator_peaks, price_col, indicator
            )

            # 检测底背离(价格创新低,指标不创新低)
            bullish_divergences = SignalDetector._detect_bullish_divergence(
                df, price_troughs, indicator_troughs, price_col, indicator
            )

            results[indicator] = {
                'bearish_divergence': bearish_divergences,
                'bullish_divergence': bullish_divergences,
                'price_peaks': price_peaks,
                'price_troughs': price_troughs,
                'indicator_peaks': indicator_peaks,
                'indicator_troughs': indicator_troughs
            }

        return results

    @staticmethod
    def _detect_bearish_divergence(df, price_peaks, indicator_peaks, price_col, indicator_col):
        """检测顶背离(看跌背离)"""
        bearish_signals = pd.Series([False] * len(df), index=df.index)

        if len(price_peaks) < 2 or len(indicator_peaks) < 2:
            return bearish_signals

        # 寻找价格峰值对应的指标峰值
        for i in range(1, len(price_peaks)):
            current_price_peak = price_peaks[i]
            prev_price_peak = price_peaks[i-1]

            # 寻找对应的指标峰值
            current_indicator_peak = SignalDetector._find_nearest_peak(
                current_price_peak, indicator_peaks, max_distance=15
            )
            prev_indicator_peak = SignalDetector._find_nearest_peak(
                prev_price_peak, indicator_peaks, max_distance=15
            )

            if current_indicator_peak is not None and prev_indicator_peak is not None:
                # 检查是否形成顶背离
                price_higher = df.iloc[current_price_peak][price_col] > df.iloc[prev_price_peak][price_col]
                indicator_lower = df.iloc[current_indicator_peak][indicator_col] < df.iloc[prev_indicator_peak][indicator_col]

                if price_higher and indicator_lower:
                    # 计算背离强度
                    price_change = (df.iloc[current_price_peak][price_col] - df.iloc[prev_price_peak][price_col]) / df.iloc[prev_price_peak][price_col]
                    indicator_change = (df.iloc[current_indicator_peak][indicator_col] - df.iloc[prev_indicator_peak][indicator_col]) / abs(df.iloc[prev_indicator_peak][indicator_col])

                    # 背离强度:价格上涨幅度与指标下跌幅度的比值
                    divergence_strength = price_change - indicator_change

                    if divergence_strength > 0.02:  # 至少2%的背离强度
                        bearish_signals.iloc[current_price_peak] = True

        return bearish_signals

    @staticmethod
    def _detect_bullish_divergence(df, price_troughs, indicator_troughs, price_col, indicator_col):
        """检测底背离(看涨背离)"""
        bullish_signals = pd.Series([False] * len(df), index=df.index)

        if len(price_troughs) < 2 or len(indicator_troughs) < 2:
            return bullish_signals

        # 寻找价格谷值对应的指标谷值
        for i in range(1, len(price_troughs)):
            current_price_trough = price_troughs[i]
            prev_price_trough = price_troughs[i-1]

            # 寻找对应的指标谷值
            current_indicator_trough = SignalDetector._find_nearest_peak(
                current_price_trough, indicator_troughs, max_distance=15
            )
            prev_indicator_trough = SignalDetector._find_nearest_peak(
                prev_price_trough, indicator_troughs, max_distance=15
            )

            if current_indicator_trough is not None and prev_indicator_trough is not None:
                # 检查是否形成底背离
                price_lower = df.iloc[current_price_trough][price_col] < df.iloc[prev_price_trough][price_col]
                indicator_higher = df.iloc[current_indicator_trough][indicator_col] > df.iloc[prev_indicator_trough][indicator_col]

                if price_lower and indicator_higher:
                    # 计算背离强度
                    price_change = (df.iloc[prev_price_trough][price_col] - df.iloc[current_price_trough][price_col]) / df.iloc[current_price_trough][price_col]
                    indicator_change = (df.iloc[current_indicator_trough][indicator_col] - df.iloc[prev_indicator_trough][indicator_col]) / abs(df.iloc[prev_indicator_trough][indicator_col])

                    # 背离强度:价格下跌幅度与指标上涨幅度的比值
                    divergence_strength = price_change + indicator_change

                    if divergence_strength > 0.02:  # 至少2%的背离强度
                        bullish_signals.iloc[current_price_trough] = True

        return bullish_signals

    @staticmethod
    def _find_nearest_peak(target_index, peak_indices, max_distance=15):
        """寻找最近的峰值索引"""
        if len(peak_indices) == 0:
            return None

        distances = np.abs(peak_indices - target_index)
        min_distance_idx = np.argmin(distances)

        if distances[min_distance_idx] <= max_distance:
            return peak_indices[min_distance_idx]

        return None



    @staticmethod
    def _calculate_all_indicators(df):
        """计算所有需要的技术指标"""
        # 确保有必要的价格数据
        required_cols = ['open', 'high', 'low', 'close']
        for col in required_cols:
            if col not in df.columns:
                print(f"警告:缺少{col}列,使用close价格代替")
                df[col] = df['close']

        # 计算RSI
        df['rsi'] = TechnicalIndicator.calculate_rsi(df['close'].values)

        # 计算MACD
        dif, dea, macd = TechnicalIndicator.calculate_macd(df['close'].values)
        df['macd_dif'] = dif
        df['macd_dea'] = dea
        df['macd_histogram'] = macd

        # 计算KDJ
        k_values, d_values, j_values = TechnicalIndicator.calculate_kdj(
            df['high'].values, df['low'].values, df['close'].values
        )
        df['kdj_k'] = k_values
        df['kdj_d'] = d_values
        df['kdj_j'] = j_values

        return df









    @staticmethod
    def _calculate_individual_divergence_strength(df, idx, results, indicator, divergence_type):
        """计算单个指标的背离强度"""
        try:
            if divergence_type == 'bullish':
                # 寻找前一个价格低点
                price_troughs = results['price_troughs']
                current_trough_idx = np.where(price_troughs == idx)[0]

                if len(current_trough_idx) > 0 and current_trough_idx[0] > 0:
                    prev_trough = price_troughs[current_trough_idx[0] - 1]

                    # 计算价格变化和指标变化
                    price_change = (df.iloc[prev_trough]['close'] - df.iloc[idx]['close']) / df.iloc[idx]['close']
                    indicator_change = (df.iloc[idx][indicator] - df.iloc[prev_trough][indicator]) / abs(df.iloc[prev_trough][indicator])

                    return abs(price_change + indicator_change)

            elif divergence_type == 'bearish':
                # 寻找前一个价格高点
                price_peaks = results['price_peaks']
                current_peak_idx = np.where(price_peaks == idx)[0]

                if len(current_peak_idx) > 0 and current_peak_idx[0] > 0:
                    prev_peak = price_peaks[current_peak_idx[0] - 1]

                    # 计算价格变化和指标变化
                    price_change = (df.iloc[idx]['close'] - df.iloc[prev_peak]['close']) / df.iloc[prev_peak]['close']
                    indicator_change = (df.iloc[prev_peak][indicator] - df.iloc[idx][indicator]) / abs(df.iloc[idx][indicator])

                    return abs(price_change + indicator_change)

        except (IndexError, KeyError, ZeroDivisionError):
            pass

        return 0.0

    @staticmethod
    def _summarize_divergences(divergence_results):
        """汇总背离检测结果"""
        summary = {}

        for indicator, results in divergence_results.items():
            bullish_count = results['bullish_divergence'].sum()
            bearish_count = results['bearish_divergence'].sum()

            summary[indicator] = {
                'bullish_divergences': int(bullish_count),
                'bearish_divergences': int(bearish_count),
                'total_divergences': int(bullish_count + bearish_count)
            }

        return summary


class StockEchartVisualizer:
    """使用Pyecharts绘制基金图表的类"""

    def __init__(self, config=None):
        """初始化StockEchartVisualizer

        Args:
            config: 配置信息字典,可包含主题、大小等设置
        """
        self.data_manager = StockDataManager()
        self.theme = "white"  # Pyecharts主题
        self.width = 1200
        self.height = 800
        self.js_renderer = "canvas"  # 渲染模式:canvas 或 svg

        if config:
            self.theme = config.get("theme", self.theme)
            self.width = config.get("width", self.width)
            self.height = config.get("height", self.height)
            self.js_renderer = config.get("renderer", self.js_renderer)

    def set_theme(self, theme):
        """设置图表主题

        Args:
            theme: Pyecharts支持的主题
        """
        self.theme = theme

    def set_size(self, width, height):
        """设置图表大小

        Args:
            width: 图表宽度
            height: 图表高度
        """
        self.width = width
        self.height = height

    def plot_stock_chart(
        self,
        ts_code,
        display_points=350,
        force_refresh=False,
        long_term=26,
        short_term=13,
        smooth_window=None,
        start_date=None,
        end_date=None,
        add_trendlines=True,
        extrema_window=20,
        min_trend_length=60,
        show_breakthrough=True,
        add_fibonacci=True,
        detect_patterns=True,
        show_traditional_signals=True,
        show_divergence_signals=True,
    ):
        """
        使用Pyecharts绘制基金图表,展示技术指标
        
        Args:
            ts_code: 基金代码
            display_points: 显示的数据点数量
            force_refresh: 是否强制刷新数据
            long_term: 长期EMA周期
            short_term: 短期EMA周期
            smooth_window: 平滑窗口大小
            start_date: 开始日期
            end_date: 结束日期
            add_trendlines: 是否添加趋势线
            extrema_window: 极值点检测窗口大小
            min_trend_length: 最小趋势长度
            show_breakthrough: 是否显示突破点
            add_fibonacci: 是否添加斐波那契回调线
            detect_patterns: 是否检测形态
            show_traditional_signals: 是否显示传统EMA/动量信号
            show_divergence_signals: 是否显示背离信号
        
        Returns:
            Grid: Pyecharts图表对象或None(数据为空时)
        """
        # 1. 获取并预处理数据
        df_full = self.data_manager.get_fund_data(
            ts_code, start_date, end_date, force_refresh
        )

        # 如果数据为空,返回None
        if df_full.empty:
            print(f"警告: 基金 {ts_code} 数据为空.")
            return None

        # 检查数据长度是否足够
        data_length = len(df_full)
        if data_length < max(long_term, short_term) + 10:
            print(f"警告: {ts_code} 数据长度({data_length})不足以可靠计算技术指标")

        # 2. 计算所有技术指标并添加到DataFrame
        df_full['ema_long'] = EMA(df_full['close'], long_term)
        df_full['ema_short'] = EMA(df_full['close'], short_term)
        df_full['ema_5'] = EMA(df_full['close'], 5)
        
        # 计算EMA差值和动量
        df_full['ema_diff'] = df_full['ema_short'] - df_full['ema_long']
        df_full['ema_diff_smoothed'] = TechnicalIndicator.smooth_data(pd.Series(df_full['ema_diff']).fillna(0), window_size=smooth_window)
        
        momentum_window = min(15, data_length // 8)
        if momentum_window % 2 == 0: momentum_window += 1
        
        df_full['momentum'] = TechnicalIndicator.smooth_data(pd.Series(df_full['ema_diff']).diff().fillna(0), window_size=momentum_window)

        # 3. 截取用于显示的数据
        if display_points > data_length:
            print(f"调整显示点数: {display_points} -> {data_length}")
            display_points = data_length
        
        start_idx = max(0, data_length - display_points)
        df = df_full.iloc[start_idx:].reset_index(drop=True)

        # 准备图表基础数据
        x_data = df['trade_date'].tolist()
        price_data = df['close'].tolist()

        # 4. 创建价格主图表
        price_chart = (
            Line()
            .add_xaxis(x_data)
            .add_yaxis(
                "价格",
                price_data,
                is_smooth=True,
                is_symbol_show=False,
                linestyle_opts=opts.LineStyleOpts(width=1, opacity=0.6),
                label_opts=opts.LabelOpts(is_show=False),
            )
            .set_global_opts(
                title_opts=opts.TitleOpts(
                    title="趋势分析",
                    subtitle=f"{ts_code}",
                    pos_left="center",
                    pos_top="0px",
                ),
                xaxis_opts=opts.AxisOpts(
                    type_="category",
                    axislabel_opts=opts.LabelOpts(is_show=False),
                    is_scale=True,
                ),
                yaxis_opts=opts.AxisOpts(
                    name="价格",
                    is_scale=True,
                    axistick_opts=opts.AxisTickOpts(is_show=True),
                    splitline_opts=opts.SplitLineOpts(is_show=True),
                    min_="dataMin",
                ),
                tooltip_opts=opts.TooltipOpts(trigger="axis", axis_pointer_type="cross"),
                legend_opts=opts.LegendOpts(
                    pos_left="5%", pos_top="5%", background_color="rgba(255, 255, 255, 0.6)"
                ),
            )
        )
        
        # 添加EMA线 (Pyecharts会自动处理NaN值,不绘制)
        price_chart.add_yaxis(
            f"{long_term}日EMA", df['ema_long'].tolist(), is_symbol_show=False, label_opts=opts.LabelOpts(is_show=False)
        )
        price_chart.add_yaxis(
            f"{short_term}日EMA", df['ema_short'].tolist(), is_symbol_show=False, label_opts=opts.LabelOpts(is_show=False)
        )
        price_chart.add_yaxis(
            "5日EMA", df['ema_5'].tolist(), is_symbol_show=False, label_opts=opts.LabelOpts(is_show=False),
            linestyle_opts=opts.LineStyleOpts(color="#FF9900")
        )

        # 5. 添加各种叠加图层 (趋势线, 形态, 信号点等)
        # 5.1 添加趋势线
        if add_trendlines:
            # 寻找极值点
            window = min(extrema_window, len(price_data) // 4)
            if window < 3: window = 3
            
            max_indices = argrelextrema(np.array(price_data), np.greater, order=window)[0]
            min_indices = argrelextrema(np.array(price_data), np.less, order=window)[0]

            trendlines_up = []
            valid_lows = sorted(min_indices)
            for i in range(len(valid_lows) - 1):
                start_rel, end_rel = valid_lows[i], valid_lows[i + 1]
                if (end_rel - start_rel) >= min_trend_length and price_data[end_rel] > price_data[start_rel]:
                    trendlines_up.append((start_rel, end_rel))

            trendlines_down = []
            valid_highs = sorted(max_indices)
            for i in range(len(valid_highs) - 1):
                start_rel, end_rel = valid_highs[i], valid_highs[i + 1]
                if (end_rel - start_rel) >= min_trend_length and price_data[end_rel] < price_data[start_rel]:
                    trendlines_down.append((start_rel, end_rel))

            # 绘制趋势线
            for start_rel, end_rel in trendlines_up + trendlines_down:
                is_uptrend = (start_rel, end_rel) in trendlines_up
                color = "green" if is_uptrend else "red"
                
                trend_x = [x_data[start_rel], x_data[end_rel]]
                trend_y = [price_data[start_rel], price_data[end_rel]]
                
                price_chart.overlap(
                    Line().add_xaxis(trend_x).add_yaxis(
                        "", trend_y, is_symbol_show=False, label_opts=opts.LabelOpts(is_show=False),
                        linestyle_opts=opts.LineStyleOpts(width=2, color=color, type_="dashed")
                    )
                )

                # [FIXED] 增加除零保护
                if end_rel > start_rel:
                    slope = (trend_y[1] - trend_y[0]) / (end_rel - start_rel)
                    if end_rel < len(x_data) - 1:
                        extended_x = [x_data[end_rel], x_data[-1]]
                        extended_y_end = [trend_y[1], trend_y[1] + slope * (len(x_data) - 1 - end_rel)]
                        price_chart.overlap(
                            Line().add_xaxis(extended_x).add_yaxis(
                                "", extended_y_end, is_symbol_show=False, label_opts=opts.LabelOpts(is_show=False),
                                linestyle_opts=opts.LineStyleOpts(width=1, color=color, type_="dotted", opacity=0.5)
                            )
                        )
            
            # 5.2 添加突破点
            if show_breakthrough:
                breakthrough_points, success_rate = detect_breakthrough(
                    df_full.iloc[start_idx:], trendlines_up, trendlines_down, price_data
                )
                if breakthrough_points:
                    break_x = [x_data[idx] for idx in breakthrough_points if idx < len(x_data)]
                    break_y = [price_data[idx] for idx in breakthrough_points if idx < len(price_data)]
                    if break_x:
                        price_chart.overlap(
                            Scatter().add_xaxis(break_x).add_yaxis(
                                f"突破点(成功率{success_rate:.0%})", break_y, symbol_size=15, symbol="diamond",
                                itemstyle_opts=opts.ItemStyleOpts(color="gold", border_color="black", border_width=1)
                            )
                        )

        # 5.3 添加斐波那契回调线
        if add_fibonacci and len(price_data) > 0:
            high_price, low_price = max(price_data), min(price_data)
            fib_levels = TechnicalIndicator.calculate_fibonacci_levels(high_price, low_price)
            for level_name, level_value in fib_levels.items():
                if "回调" in level_name:
                    price_chart.overlap(
                        Line().add_xaxis([x_data[0], x_data[-1]]).add_yaxis(
                            f"斐波那契{level_name}", [level_value, level_value], is_symbol_show=False,
                            label_opts=opts.LabelOpts(is_show=False),
                            linestyle_opts=opts.LineStyleOpts(width=1.5, type_="dashed", color="#6A5ACD", opacity=0.7)
                        )
                    )

        # 5.4 添加形态识别 - 增强版
        if detect_patterns and len(price_data) >= 60:
            pattern_window = max(10, int(len(price_data) * 0.08))

            # 检测各种形态
            hs_tops, hs_bottoms = TechnicalIndicator.detect_head_shoulders(price_data, window_size=pattern_window)
            double_tops, double_bottoms = TechnicalIndicator.detect_double_top_bottom(price_data, window_size=pattern_window)
            asc_triangles, desc_triangles, sym_triangles = TechnicalIndicator.detect_triangle_patterns(price_data, window_size=pattern_window)
            rising_wedges, falling_wedges = TechnicalIndicator.detect_wedge_patterns(price_data, window_size=pattern_window)
            rectangles = TechnicalIndicator.detect_rectangle_patterns(price_data, window_size=pattern_window)

            # 绘制头肩形态
            for pattern_name, patterns, color in [("头肩顶", hs_tops, "#FF4500"), ("头肩底", hs_bottoms, "#4169E1")]:
                for p in patterns:
                    if len(p) >= 3:
                        x = [x_data[p[0]], x_data[p[1]], x_data[p[2]]]
                        y = [price_data[p[0]], price_data[p[1]], price_data[p[2]]]
                        price_chart.overlap(
                            Line().add_xaxis(x).add_yaxis(
                                pattern_name, y, is_symbol_show=True, symbol_size=8,
                                linestyle_opts=opts.LineStyleOpts(color=color, width=2, type_="dashed"),
                                label_opts=opts.LabelOpts(is_show=False)
                            )
                        )

            # 绘制双顶/双底形态
            for pattern_name, patterns, color in [("双顶", double_tops, "#DC143C"), ("双底", double_bottoms, "#228B22")]:
                for p in patterns:
                    if len(p) >= 3:
                        # 绘制两个顶/底点和颈线
                        top_x = [x_data[p[0]], x_data[p[1]]]
                        top_y = [price_data[p[0]], price_data[p[1]]]
                        neck_x = [x_data[p[0]], x_data[p[1]]]
                        neck_y = [price_data[p[2]], price_data[p[2]]]

                        price_chart.overlap(
                            Scatter().add_xaxis(top_x).add_yaxis(
                                pattern_name, top_y, symbol_size=12, symbol="circle",
                                itemstyle_opts=opts.ItemStyleOpts(color=color),
                                label_opts=opts.LabelOpts(is_show=False)
                            )
                        )
                        price_chart.overlap(
                            Line().add_xaxis(neck_x).add_yaxis(
                                f"{pattern_name}颈线", neck_y, is_symbol_show=False,
                                linestyle_opts=opts.LineStyleOpts(color=color, width=1, type_="dotted"),
                                label_opts=opts.LabelOpts(is_show=False)
                            )
                        )

            # 绘制三角形形态
            triangle_patterns = [
                ("上升三角", asc_triangles, "#FF6347"),
                ("下降三角", desc_triangles, "#4682B4"),
                ("对称三角", sym_triangles, "#9370DB")
            ]
            for pattern_name, patterns, color in triangle_patterns:
                for pattern in patterns:
                    if 'resistance_points' in pattern and 'support_points' in pattern:
                        # 绘制阻力线
                        res_points = pattern['resistance_points']
                        if len(res_points) >= 2:
                            res_x = [x_data[res_points[0]], x_data[res_points[-1]]]
                            res_y = [price_data[res_points[0]], price_data[res_points[-1]]]
                            price_chart.overlap(
                                Line().add_xaxis(res_x).add_yaxis(
                                    f"{pattern_name}阻力", res_y, is_symbol_show=False,
                                    linestyle_opts=opts.LineStyleOpts(color=color, width=2),
                                    label_opts=opts.LabelOpts(is_show=False)
                                )
                            )

                        # 绘制支撑线
                        sup_points = pattern['support_points']
                        if len(sup_points) >= 2:
                            sup_x = [x_data[sup_points[0]], x_data[sup_points[-1]]]
                            sup_y = [price_data[sup_points[0]], price_data[sup_points[-1]]]
                            price_chart.overlap(
                                Line().add_xaxis(sup_x).add_yaxis(
                                    f"{pattern_name}支撑", sup_y, is_symbol_show=False,
                                    linestyle_opts=opts.LineStyleOpts(color=color, width=2),
                                    label_opts=opts.LabelOpts(is_show=False)
                                )
                            )

            # 绘制楔形形态
            wedge_patterns = [("上升楔形", rising_wedges, "#FF1493"), ("下降楔形", falling_wedges, "#00CED1")]
            for pattern_name, patterns, color in wedge_patterns:
                for pattern in patterns:
                    if 'resistance_points' in pattern and 'support_points' in pattern:
                        res_points = pattern['resistance_points']
                        sup_points = pattern['support_points']

                        if len(res_points) >= 2 and len(sup_points) >= 2:
                            # 绘制楔形的两条边
                            res_x = [x_data[res_points[0]], x_data[res_points[-1]]]
                            res_y = [price_data[res_points[0]], price_data[res_points[-1]]]
                            sup_x = [x_data[sup_points[0]], x_data[sup_points[-1]]]
                            sup_y = [price_data[sup_points[0]], price_data[sup_points[-1]]]

                            price_chart.overlap(
                                Line().add_xaxis(res_x).add_yaxis(
                                    pattern_name, res_y, is_symbol_show=False,
                                    linestyle_opts=opts.LineStyleOpts(color=color, width=2, type_="dashed"),
                                    label_opts=opts.LabelOpts(is_show=False)
                                )
                            )
                            price_chart.overlap(
                                Line().add_xaxis(sup_x).add_yaxis(
                                    f"{pattern_name}下轨", sup_y, is_symbol_show=False,
                                    linestyle_opts=opts.LineStyleOpts(color=color, width=2, type_="dashed"),
                                    label_opts=opts.LabelOpts(is_show=False)
                                )
                            )

            # 绘制矩形整理形态
            for rect in rectangles:
                if 'resistance_level' in rect and 'support_level' in rect:
                    res_level = rect['resistance_level']
                    sup_level = rect['support_level']

                    # 绘制水平阻力线和支撑线
                    price_chart.overlap(
                        Line().add_xaxis([x_data[0], x_data[-1]]).add_yaxis(
                            "矩形阻力", [res_level, res_level], is_symbol_show=False,
                            linestyle_opts=opts.LineStyleOpts(color="#8A2BE2", width=2, type_="solid"),
                            label_opts=opts.LabelOpts(is_show=False)
                        )
                    )
                    price_chart.overlap(
                        Line().add_xaxis([x_data[0], x_data[-1]]).add_yaxis(
                            "矩形支撑", [sup_level, sup_level], is_symbol_show=False,
                            linestyle_opts=opts.LineStyleOpts(color="#8A2BE2", width=2, type_="solid"),
                            label_opts=opts.LabelOpts(is_show=False)
                        )
                    )

        # 5.5 添加增强版买卖信号点
        try:
            # 使用新的信号检测器
            buy_signals_df, sell_signals_df, signal_quality = SignalDetector.detect_enhanced_signals(
                df, ema_short_col='ema_short', ema_long_col='ema_long',
                momentum_col='momentum', volume_col='volume'
            )

            # 检测多指标背离信号
            try:
                # 计算所需的技术指标
                df_with_indicators = SignalDetector._calculate_all_indicators(df.copy())

                # 生成背离交易信号(使用较宽松的参数以便在图表中显示更多信号)
                divergence_buy_df, divergence_sell_df, divergence_stats = SignalDetector.generate_divergence_trading_signals(
                    df_with_indicators,
                    min_signal_interval=3,
                    volume_confirmation=False,  # 关闭成交量确认以获得更多信号
                    min_quality_score=0.3,     # 降低质量要求
                    indicators=['rsi', 'macd_dif', 'kdj_k']
                )

                # 获取详细的背离检测结果用于可视化
                divergence_results = SignalDetector.detect_multi_indicator_divergence(
                    df_with_indicators,
                    indicators=['rsi', 'macd_dif', 'kdj_k']
                )

                print(f"背离信号统计: {divergence_stats}")

            except Exception as e:
                print(f"背离信号检测失败,使用基础背离检测: {e}")
                # 回退到基础背离检测
                bearish_div, bullish_div = SignalDetector.detect_divergence_signals(
                    df, price_col='close', momentum_col='momentum'
                )
                divergence_buy_df = pd.DataFrame()
                divergence_sell_df = pd.DataFrame()
                divergence_results = {}
                divergence_stats = {}

            # 绘制传统EMA/动量信号
            if show_traditional_signals:
                if not buy_signals_df.empty:
                    # 根据质量评分调整信号点大小和颜色
                    quality_scores = buy_signals_df.get('quality_score')
                    if quality_scores is not None:
                        buy_sizes = [max(10, min(20, score * 20)) for score in quality_scores]
                    else:
                        buy_sizes = 15  # 默认大小

                    price_chart.overlap(
                        Scatter().add_xaxis(list(buy_signals_df['trade_date'])).add_yaxis(
                            f"传统买入信号(质量:{signal_quality.get('avg_buy_quality', 0):.2f})",
                            list(buy_signals_df['close']),
                            symbol_size=buy_sizes, symbol="triangle",
                            itemstyle_opts=opts.ItemStyleOpts(color="green", border_color="darkgreen", border_width=2),
                            label_opts=opts.LabelOpts(is_show=False)
                        )
                    )

                if not sell_signals_df.empty:
                    quality_scores = sell_signals_df.get('quality_score')
                    if quality_scores is not None:
                        sell_sizes = [max(10, min(20, score * 20)) for score in quality_scores]
                    else:
                        sell_sizes = 15  # 默认大小

                    price_chart.overlap(
                        Scatter().add_xaxis(list(sell_signals_df['trade_date'])).add_yaxis(
                            f"传统卖出信号(质量:{signal_quality.get('avg_sell_quality', 0):.2f})",
                            list(sell_signals_df['close']),
                            symbol_size=sell_sizes, symbol="triangle-down",
                            itemstyle_opts=opts.ItemStyleOpts(color="red", border_color="darkred", border_width=2),
                            label_opts=opts.LabelOpts(is_show=False)
                        )
                    )

            # 绘制多指标背离交易信号
            if show_divergence_signals and not divergence_buy_df.empty:
                # 根据信号强度调整显示大小
                buy_sizes = []
                for _, row in divergence_buy_df.iterrows():
                    strength = row.get('signal_strength', 0.5)
                    size = max(15, min(25, int(strength * 30)))
                    buy_sizes.append(size)

                price_chart.overlap(
                    Scatter().add_xaxis(list(divergence_buy_df['trade_date'])).add_yaxis(
                        f"背离买入信号({len(divergence_buy_df)}个)",
                        list(divergence_buy_df['close']),
                        symbol_size=buy_sizes, symbol="arrow-up",
                        itemstyle_opts=opts.ItemStyleOpts(color="lime", border_color="darkgreen", border_width=3),
                        label_opts=opts.LabelOpts(is_show=False)
                    )
                )

            if show_divergence_signals and not divergence_sell_df.empty:
                # 根据信号强度调整显示大小
                sell_sizes = []
                for _, row in divergence_sell_df.iterrows():
                    strength = row.get('signal_strength', 0.5)
                    size = max(15, min(25, int(strength * 30)))
                    sell_sizes.append(size)

                price_chart.overlap(
                    Scatter().add_xaxis(list(divergence_sell_df['trade_date'])).add_yaxis(
                        f"背离卖出信号({len(divergence_sell_df)}个)",
                        list(divergence_sell_df['close']),
                        symbol_size=sell_sizes, symbol="arrow-down",
                        itemstyle_opts=opts.ItemStyleOpts(color="magenta", border_color="darkred", border_width=3),
                        label_opts=opts.LabelOpts(is_show=False)
                    )
                )

            # 绘制背离线条和标记点
            if show_divergence_signals and divergence_results:
                self._add_divergence_visualization(price_chart, df_with_indicators, divergence_results, x_data, price_data)

            # 如果使用基础背离检测,绘制基础背离信号
            if 'bearish_div' in locals() and 'bullish_div' in locals():
                bearish_div_points = df[bearish_div]
                bullish_div_points = df[bullish_div]

                if not bearish_div_points.empty:
                    price_chart.overlap(
                        Scatter().add_xaxis(list(bearish_div_points['trade_date'])).add_yaxis(
                            "基础顶背离", list(bearish_div_points['close']),
                            symbol_size=12, symbol="diamond",
                            itemstyle_opts=opts.ItemStyleOpts(color="orange", border_color="darkorange", border_width=1),
                            label_opts=opts.LabelOpts(is_show=False)
                        )
                    )

                if not bullish_div_points.empty:
                    price_chart.overlap(
                        Scatter().add_xaxis(list(bullish_div_points['trade_date'])).add_yaxis(
                            "基础底背离", list(bullish_div_points['close']),
                            symbol_size=12, symbol="diamond",
                            itemstyle_opts=opts.ItemStyleOpts(color="cyan", border_color="darkcyan", border_width=1),
                            label_opts=opts.LabelOpts(is_show=False)
                        )
                    )

        except Exception as e:
            print(f"信号检测出错,使用基础信号: {e}")
            # 回退到基础信号检测
            crossover = (df['momentum'].shift(1) <= 0) & (df['momentum'] > 0)
            crossunder = (df['momentum'].shift(1) >= 0) & (df['momentum'] < 0)

            buy_points = df[crossover]
            sell_points = df[crossunder]

            if not buy_points.empty:
                price_chart.overlap(
                    Scatter().add_xaxis(list(buy_points['trade_date'])).add_yaxis(
                        "买入信号", list(buy_points['close']), symbol_size=15, symbol="triangle",
                        itemstyle_opts=opts.ItemStyleOpts(color="green"),
                        label_opts=opts.LabelOpts(is_show=False)
                    )
                )
            if not sell_points.empty:
                price_chart.overlap(
                    Scatter().add_xaxis(list(sell_points['trade_date'])).add_yaxis(
                        "卖出信号", list(sell_points['close']), symbol_size=15, symbol="triangle-down",
                        itemstyle_opts=opts.ItemStyleOpts(color="red"),
                        label_opts=opts.LabelOpts(is_show=False)
                    )
                )

        # 6. 创建附图 (动量图和趋势差异图)
        # 6.1 动量图
        momentum_data = df['momentum'].tolist()
        delta_max = df['momentum'].abs().max()
        scale_factor = min(300 / delta_max, 15) if delta_max > 0 else 10
        scaled_momentum = [v * scale_factor for v in momentum_data]
        
        momentum_chart = (
            Line().add_xaxis(x_data)
            .add_yaxis("动量指标", scaled_momentum, is_smooth=True, is_symbol_show=False,
                       linestyle_opts=opts.LineStyleOpts(width=1.5, color="blue"))
            .set_global_opts(
                xaxis_opts=opts.AxisOpts(axislabel_opts=opts.LabelOpts(is_show=False)),
                yaxis_opts=opts.AxisOpts(name="动量", is_scale=True),
                tooltip_opts=opts.TooltipOpts(trigger="axis"),
                legend_opts=opts.LegendOpts(pos_left="5%", pos_top="36%"),
            )
            .set_series_opts(markline_opts=opts.MarkLineOpts(data=[opts.MarkLineItem(y=0)]))
        )

        # 6.2 趋势差异图
        trend_chart = (
            Line().add_xaxis(x_data)
            .add_yaxis("长期趋势值", (df['ema_diff'] * 2).tolist(), is_smooth=True, is_symbol_show=False,
                       linestyle_opts=opts.LineStyleOpts(width=1, opacity=0.5, color="#800080"))
            .add_yaxis("EWMA平滑", (df['ema_diff_smoothed'] * 2).tolist(), is_smooth=True, is_symbol_show=False,
                       linestyle_opts=opts.LineStyleOpts(width=2, color="#1E90FF"))
            .set_global_opts(
                xaxis_opts=opts.AxisOpts(type_="category", axislabel_opts=opts.LabelOpts(rotate=45)),
                yaxis_opts=opts.AxisOpts(name="趋势差异", is_scale=True),
                tooltip_opts=opts.TooltipOpts(trigger="axis"),
                legend_opts=opts.LegendOpts(pos_left="5%", pos_top="66%"),
            )
            .set_series_opts(markline_opts=opts.MarkLineOpts(data=[opts.MarkLineItem(y=0)]))
        )

        # 7. 组合图表
        grid = Grid(
            init_opts=opts.InitOpts(
                width=f"{self.width}px", height=f"{self.height}px", theme=self.theme,
                renderer=self.js_renderer, page_title="趋势分析"
            )
        )
        grid.add(price_chart, grid_opts=opts.GridOpts(pos_top="15%", pos_bottom="55%"))
        grid.add(momentum_chart, grid_opts=opts.GridOpts(pos_top="50%", pos_bottom="30%"))
        grid.add(trend_chart, grid_opts=opts.GridOpts(pos_top="75%", pos_bottom="5%"))

        return grid

    def _add_divergence_visualization(self, price_chart, df, divergence_results, x_data, price_data):
        """添加背离可视化元素"""
        try:
            # 为每个指标添加背离标记
            colors = {'rsi': '#FF6B6B', 'macd_dif': '#4ECDC4', 'kdj_k': '#45B7D1'}

            for indicator, results in divergence_results.items():
                color = colors.get(indicator, '#95A5A6')

                # 绘制顶背离点
                bearish_indices = results['bearish_divergence'][results['bearish_divergence']].index.tolist()
                if bearish_indices:
                    bearish_x = [x_data[i] for i in bearish_indices if i < len(x_data)]
                    bearish_y = [price_data[i] for i in bearish_indices if i < len(price_data)]

                    if bearish_x:
                        price_chart.overlap(
                            Scatter().add_xaxis(bearish_x).add_yaxis(
                                f"{indicator.upper()}顶背离",
                                bearish_y,
                                symbol_size=8, symbol="circle",
                                itemstyle_opts=opts.ItemStyleOpts(color=color, opacity=0.7),
                                label_opts=opts.LabelOpts(is_show=False)
                            )
                        )

                # 绘制底背离点
                bullish_indices = results['bullish_divergence'][results['bullish_divergence']].index.tolist()
                if bullish_indices:
                    bullish_x = [x_data[i] for i in bullish_indices if i < len(x_data)]
                    bullish_y = [price_data[i] for i in bullish_indices if i < len(price_data)]

                    if bullish_x:
                        price_chart.overlap(
                            Scatter().add_xaxis(bullish_x).add_yaxis(
                                f"{indicator.upper()}底背离",
                                bullish_y,
                                symbol_size=8, symbol="circle",
                                itemstyle_opts=opts.ItemStyleOpts(color=color, opacity=0.7),
                                label_opts=opts.LabelOpts(is_show=False)
                            )
                        )

                # 绘制背离连接线
                self._draw_divergence_lines(price_chart, df, results, x_data, price_data, color, indicator)

        except Exception as e:
            print(f"背离可视化添加失败: {e}")

    def _draw_divergence_lines(self, price_chart, df, results, x_data, price_data, color, indicator):
        """绘制背离连接线"""
        try:
            # 绘制顶背离连接线
            price_peaks = results['price_peaks']
            if len(price_peaks) >= 2:
                for i in range(1, len(price_peaks)):
                    current_peak = price_peaks[i]
                    prev_peak = price_peaks[i-1]

                    # 检查是否存在背离
                    if (current_peak < len(df) and prev_peak < len(df) and
                        results['bearish_divergence'].iloc[current_peak]):

                        line_x = [x_data[prev_peak], x_data[current_peak]]
                        line_y = [price_data[prev_peak], price_data[current_peak]]

                        price_chart.overlap(
                            Line().add_xaxis(line_x).add_yaxis(
                                "",
                                line_y,
                                is_symbol_show=False,
                                linestyle_opts=opts.LineStyleOpts(
                                    color=color, width=2, type_="dashed", opacity=0.6
                                ),
                                label_opts=opts.LabelOpts(is_show=False)
                            )
                        )

            # 绘制底背离连接线
            price_troughs = results['price_troughs']
            if len(price_troughs) >= 2:
                for i in range(1, len(price_troughs)):
                    current_trough = price_troughs[i]
                    prev_trough = price_troughs[i-1]

                    # 检查是否存在背离
                    if (current_trough < len(df) and prev_trough < len(df) and
                        results['bullish_divergence'].iloc[current_trough]):

                        line_x = [x_data[prev_trough], x_data[current_trough]]
                        line_y = [price_data[prev_trough], price_data[current_trough]]

                        price_chart.overlap(
                            Line().add_xaxis(line_x).add_yaxis(
                                "",
                                line_y,
                                is_symbol_show=False,
                                linestyle_opts=opts.LineStyleOpts(
                                    color=color, width=2, type_="dashed", opacity=0.6
                                ),
                                label_opts=opts.LabelOpts(is_show=False)
                            )
                        )

        except Exception as e:
            print(f"背离连接线绘制失败: {e}")


class DivergenceBacktester:
    """背离信号回测器"""

    def __init__(self, initial_capital=100000, commission_rate=0.001, slippage=0.001):
        """初始化回测器

        Args:
            initial_capital: 初始资金
            commission_rate: 手续费率
            slippage: 滑点率
        """
        self.initial_capital = initial_capital
        self.commission_rate = commission_rate
        self.slippage = slippage

    def backtest_divergence_signals(self, df, buy_signals_df, sell_signals_df,
                                  holding_period=10, stop_loss_pct=0.05, take_profit_pct=0.10):
        """回测背离信号

        Args:
            df: 价格数据DataFrame
            buy_signals_df: 买入信号DataFrame
            sell_signals_df: 卖出信号DataFrame
            holding_period: 最大持仓天数
            stop_loss_pct: 止损百分比
            take_profit_pct: 止盈百分比

        Returns:
            dict: 回测结果
        """
        if buy_signals_df.empty and sell_signals_df.empty:
            return self._empty_backtest_result()

        # 合并买卖信号
        all_signals = []

        for _, signal in buy_signals_df.iterrows():
            all_signals.append({
                'date': signal['trade_date'],
                'price': signal['close'],
                'type': 'buy',
                'quality': signal.get('quality_score', 0.5),
                'strength': signal.get('signal_strength', 0.5)
            })

        for _, signal in sell_signals_df.iterrows():
            all_signals.append({
                'date': signal['trade_date'],
                'price': signal['close'],
                'type': 'sell',
                'quality': signal.get('quality_score', 0.5),
                'strength': signal.get('signal_strength', 0.5)
            })

        # 按日期排序
        all_signals.sort(key=lambda x: x['date'])

        # 执行回测
        trades = []
        current_position = None
        capital = self.initial_capital

        for signal in all_signals:
            signal_date = signal['date']
            signal_price = signal['price']
            signal_type = signal['type']

            # 获取信号日期在DataFrame中的位置
            signal_idx = df[df['trade_date'] == signal_date].index
            if len(signal_idx) == 0:
                continue
            signal_idx = signal_idx[0]

            if signal_type == 'buy' and current_position is None:
                # 开多仓
                shares = int(capital / (signal_price * (1 + self.commission_rate + self.slippage)))
                if shares > 0:
                    current_position = {
                        'type': 'long',
                        'entry_date': signal_date,
                        'entry_price': signal_price * (1 + self.commission_rate + self.slippage),
                        'shares': shares,
                        'entry_idx': signal_idx,
                        'quality': signal['quality'],
                        'strength': signal['strength']
                    }
                    capital -= shares * current_position['entry_price']

            elif signal_type == 'sell' and current_position is None:
                # 开空仓(简化处理,实际中需要融券)
                shares = int(capital / (signal_price * (1 + self.commission_rate + self.slippage)))
                if shares > 0:
                    current_position = {
                        'type': 'short',
                        'entry_date': signal_date,
                        'entry_price': signal_price * (1 - self.commission_rate - self.slippage),
                        'shares': shares,
                        'entry_idx': signal_idx,
                        'quality': signal['quality'],
                        'strength': signal['strength']
                    }
                    capital += shares * current_position['entry_price']

            # 检查平仓条件
            if current_position is not None:
                exit_price, exit_reason = self._check_exit_conditions(
                    df, current_position, signal_idx, holding_period,
                    stop_loss_pct, take_profit_pct
                )

                if exit_price is not None:
                    trade_result = self._close_position(
                        current_position, exit_price, signal_date, exit_reason
                    )
                    trades.append(trade_result)

                    # 更新资金
                    if current_position['type'] == 'long':
                        capital += trade_result['shares'] * exit_price
                    else:  # short
                        capital += trade_result['shares'] * (2 * current_position['entry_price'] - exit_price)

                    current_position = None

        # 强制平仓剩余持仓
        if current_position is not None:
            last_price = df.iloc[-1]['close']
            trade_result = self._close_position(
                current_position, last_price, df.iloc[-1]['trade_date'], 'end_of_data'
            )
            trades.append(trade_result)

            if current_position['type'] == 'long':
                capital += trade_result['shares'] * last_price
            else:
                capital += trade_result['shares'] * (2 * current_position['entry_price'] - last_price)

        return self._calculate_backtest_metrics(trades, capital)

    def _check_exit_conditions(self, df, position, current_idx, holding_period,
                              stop_loss_pct, take_profit_pct):
        """检查平仓条件"""
        entry_price = position['entry_price']
        position_type = position['type']
        entry_idx = position['entry_idx']

        # 检查持仓时间
        if current_idx - entry_idx >= holding_period:
            return df.iloc[current_idx]['close'], 'holding_period_exceeded'

        # 检查止损止盈
        current_price = df.iloc[current_idx]['close']

        if position_type == 'long':
            # 多仓止损止盈
            if current_price <= entry_price * (1 - stop_loss_pct):
                return current_price, 'stop_loss'
            elif current_price >= entry_price * (1 + take_profit_pct):
                return current_price, 'take_profit'
        else:  # short
            # 空仓止损止盈
            if current_price >= entry_price * (1 + stop_loss_pct):
                return current_price, 'stop_loss'
            elif current_price <= entry_price * (1 - take_profit_pct):
                return current_price, 'take_profit'

        return None, None

    def _close_position(self, position, exit_price, exit_date, exit_reason):
        """平仓并计算交易结果"""
        entry_price = position['entry_price']
        shares = position['shares']
        position_type = position['type']

        # 计算实际平仓价格(考虑手续费和滑点)
        if position_type == 'long':
            actual_exit_price = exit_price * (1 - self.commission_rate - self.slippage)
            pnl = shares * (actual_exit_price - entry_price)
            return_pct = (actual_exit_price - entry_price) / entry_price
        else:  # short
            actual_exit_price = exit_price * (1 + self.commission_rate + self.slippage)
            pnl = shares * (entry_price - actual_exit_price)
            return_pct = (entry_price - actual_exit_price) / entry_price

        return {
            'entry_date': position['entry_date'],
            'exit_date': exit_date,
            'entry_price': entry_price,
            'exit_price': actual_exit_price,
            'shares': shares,
            'position_type': position_type,
            'pnl': pnl,
            'return_pct': return_pct,
            'exit_reason': exit_reason,
            'quality': position['quality'],
            'strength': position['strength']
        }

    def _calculate_backtest_metrics(self, trades, final_capital):
        """计算回测指标"""
        if not trades:
            return self._empty_backtest_result()

        total_trades = len(trades)
        winning_trades = [t for t in trades if t['pnl'] > 0]
        losing_trades = [t for t in trades if t['pnl'] <= 0]

        win_rate = len(winning_trades) / total_trades if total_trades > 0 else 0

        total_return = (final_capital - self.initial_capital) / self.initial_capital

        returns = [t['return_pct'] for t in trades]
        avg_return = np.mean(returns) if returns else 0
        std_return = np.std(returns) if len(returns) > 1 else 0

        sharpe_ratio = avg_return / std_return if std_return > 0 else 0

        max_drawdown = self._calculate_max_drawdown(trades)

        avg_win = np.mean([t['pnl'] for t in winning_trades]) if winning_trades else 0
        avg_loss = np.mean([t['pnl'] for t in losing_trades]) if losing_trades else 0

        profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else float('inf')

        return {
            'total_trades': total_trades,
            'win_rate': win_rate,
            'total_return': total_return,
            'final_capital': final_capital,
            'avg_return_per_trade': avg_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'profit_factor': profit_factor,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'trades': trades,
            'quality_analysis': self._analyze_signal_quality(trades)
        }

    def _calculate_max_drawdown(self, trades):
        """计算最大回撤"""
        if not trades:
            return 0

        cumulative_pnl = 0
        peak = 0
        max_dd = 0

        for trade in trades:
            cumulative_pnl += trade['pnl']
            if cumulative_pnl > peak:
                peak = cumulative_pnl

            drawdown = (peak - cumulative_pnl) / self.initial_capital
            if drawdown > max_dd:
                max_dd = drawdown

        return max_dd

    def _analyze_signal_quality(self, trades):
        """分析信号质量与交易结果的关系"""
        if not trades:
            return {}

        high_quality_trades = [t for t in trades if t['quality'] >= 0.7]
        medium_quality_trades = [t for t in trades if 0.4 <= t['quality'] < 0.7]
        low_quality_trades = [t for t in trades if t['quality'] < 0.4]

        def analyze_group(group, name):
            if not group:
                return {f'{name}_count': 0, f'{name}_win_rate': 0, f'{name}_avg_return': 0}

            win_rate = len([t for t in group if t['pnl'] > 0]) / len(group)
            avg_return = np.mean([t['return_pct'] for t in group])

            return {
                f'{name}_count': len(group),
                f'{name}_win_rate': win_rate,
                f'{name}_avg_return': avg_return
            }

        analysis = {}
        analysis.update(analyze_group(high_quality_trades, 'high_quality'))
        analysis.update(analyze_group(medium_quality_trades, 'medium_quality'))
        analysis.update(analyze_group(low_quality_trades, 'low_quality'))

        return analysis

    def _empty_backtest_result(self):
        """返回空的回测结果"""
        return {
            'total_trades': 0,
            'win_rate': 0,
            'total_return': 0,
            'final_capital': self.initial_capital,
            'avg_return_per_trade': 0,
            'sharpe_ratio': 0,
            'max_drawdown': 0,
            'profit_factor': 0,
            'avg_win': 0,
            'avg_loss': 0,
            'trades': [],
            'quality_analysis': {}
        }


def detect_breakthrough(
    df,
    trendlines_up,
    trendlines_down,
    price_data,
    tolerance=0.01
):
    """
    检测价格对趋势线的突破点
    
    Args:
        df: 数据框
        trendlines_up: 上升趋势线列表
        trendlines_down: 下降趋势线列表
        price_data: 价格数据
        tolerance: 突破容差
        
    Returns:
        tuple: (突破点列表, 成功率)
    """
    breakthrough_points = []
    success_count = 0
    total_count = 0

    if "volume" in df.columns:
        volume_data = df["volume"].values
        volume_ma10 = df["volume"].rolling(window=10).mean().values
    else:
        volume_data = None
        volume_ma10 = None

    # 检查上升趋势线突破
    for start_rel, end_rel in trendlines_up:
        # 增加除零保护
        if end_rel <= start_rel:
            continue
        slope = (price_data[end_rel] - price_data[start_rel]) / (end_rel - start_rel)

        for i in range(end_rel + 1, min(end_rel + 15, len(price_data))):
            expected_value = price_data[start_rel] + slope * (i - start_rel)
            actual_value = price_data[i]

            if actual_value < expected_value * (1 - tolerance):
                total_count += 1
                breakthrough_points.append(i)
                break

    # 检查下降趋势线突破
    for start_rel, end_rel in trendlines_down:
        # 增加除零保护
        if end_rel <= start_rel:
            continue
        slope = (price_data[end_rel] - price_data[start_rel]) / (end_rel - start_rel)

        for i in range(end_rel + 1, min(end_rel + 15, len(price_data))):
            expected_value = price_data[start_rel] + slope * (i - start_rel)
            actual_value = price_data[i]

            if actual_value > expected_value * (1 + tolerance):
                total_count += 1
                breakthrough_points.append(i)
                break

    success_rate = success_count / total_count if total_count > 0 else 0
    return breakthrough_points, success_rate





