#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的背离交易信号系统测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from indicator_analysis import (
    StockDataManager, SignalDetector, DivergenceBacktester, 
    StockEchartVisualizer, TechnicalIndicator
)
import pandas as pd
import numpy as np

def main():
    print("=" * 60)
    print("背离交易信号系统简化测试")
    print("=" * 60)
    
    # 初始化数据管理器
    try:
        data_manager = StockDataManager()
        print("[OK] 数据管理器初始化成功")
    except Exception as e:
        print(f"[ERROR] 数据管理器初始化失败: {e}")
        return
    
    # 获取测试数据
    fund_code = "513260.SH"  # 恒生科技ETF
    print(f"\n测试基金: {fund_code}")
    
    try:
        df = data_manager.get_fund_data(fund_code, start_date="20240101")
        if df.empty:
            print(f"[ERROR] 无法获取 {fund_code} 的数据")
            return
        
        print(f"[OK] 获取到 {len(df)} 条数据记录")
        
        # 计算技术指标
        print("\n计算技术指标...")
        df_with_indicators = SignalDetector._calculate_all_indicators(df.copy())
        
        # 检查指标计算结果
        rsi_valid = np.sum(~np.isnan(df_with_indicators['rsi']))
        macd_valid = np.sum(~np.isnan(df_with_indicators['macd_dif']))
        kdj_valid = np.sum(~np.isnan(df_with_indicators['kdj_k']))
        
        print(f"  RSI有效值: {rsi_valid}")
        print(f"  MACD有效值: {macd_valid}")
        print(f"  KDJ有效值: {kdj_valid}")
        
        # 检测背离
        print("\n检测背离信号...")
        divergence_results = SignalDetector.detect_multi_indicator_divergence(
            df_with_indicators, 
            indicators=['rsi', 'macd_dif', 'kdj_k']
        )
        
        total_bullish = 0
        total_bearish = 0
        for indicator, results in divergence_results.items():
            bullish_count = results['bullish_divergence'].sum()
            bearish_count = results['bearish_divergence'].sum()
            total_bullish += bullish_count
            total_bearish += bearish_count
            print(f"  {indicator}: 底背离 {bullish_count}个, 顶背离 {bearish_count}个")
        
        print(f"  总计: 底背离 {total_bullish}个, 顶背离 {total_bearish}个")
        
        # 生成交易信号
        print("\n生成交易信号...")
        buy_signals, sell_signals, stats = SignalDetector.generate_divergence_trading_signals(
            df_with_indicators,
            min_signal_interval=5,
            volume_confirmation=False,
            min_quality_score=0.3,
            indicators=['rsi', 'macd_dif', 'kdj_k']
        )
        
        print(f"  买入信号: {len(buy_signals)}个")
        print(f"  卖出信号: {len(sell_signals)}个")
        print(f"  平均买入质量: {stats.get('avg_buy_quality', 0):.3f}")
        print(f"  平均卖出质量: {stats.get('avg_sell_quality', 0):.3f}")
        
        # 回测验证
        if not buy_signals.empty or not sell_signals.empty:
            print("\n执行回测...")
            backtester = DivergenceBacktester(
                initial_capital=100000,
                commission_rate=0.001,
                slippage=0.001
            )
            
            results = backtester.backtest_divergence_signals(
                df_with_indicators, buy_signals, sell_signals,
                holding_period=10,
                stop_loss_pct=0.05,
                take_profit_pct=0.10
            )
            
            print(f"  总交易次数: {results['total_trades']}")
            print(f"  胜率: {results['win_rate']:.2%}")
            print(f"  总收益率: {results['total_return']:.2%}")
            print(f"  最大回撤: {results['max_drawdown']:.2%}")
        else:
            print("\n[WARN] 无交易信号，跳过回测")
        
        # 生成可视化
        print("\n生成可视化图表...")
        try:
            visualizer = StockEchartVisualizer()
            chart = visualizer.plot_stock_chart(
                fund_code,
                display_points=min(200, len(df_with_indicators)),
                force_refresh=False,
                detect_patterns=True
            )
            
            if chart is not None:
                output_file = f"cache/simple_divergence_test_{fund_code.replace('.', '_')}.html"
                chart.render(output_file)
                print(f"  [OK] 图表保存至: {output_file}")
            else:
                print("  [WARN] 图表生成失败")
        except Exception as e:
            print(f"  [ERROR] 可视化失败: {e}")
        
        print("\n[OK] 测试完成！")
        
        # 输出总结
        print("\n" + "=" * 60)
        print("测试总结:")
        print(f"  基金代码: {fund_code}")
        print(f"  数据记录: {len(df)} 条")
        print(f"  技术指标: RSI, MACD, KDJ 计算正常")
        print(f"  背离检测: 底背离 {total_bullish}个, 顶背离 {total_bearish}个")
        print(f"  交易信号: 买入 {len(buy_signals)}个, 卖出 {len(sell_signals)}个")
        print("  系统功能: 正常运行")
        print("=" * 60)
        
    except Exception as e:
        print(f"[ERROR] 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
