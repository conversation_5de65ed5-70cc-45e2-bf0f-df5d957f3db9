#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
背离交易信号系统测试脚本
测试新实现的多指标背离检测和交易信号生成功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from indicator_analysis import StockDataManager, SignalDetector, DivergenceBacktester, StockEchartVisualizer
import pandas as pd
import numpy as np

def test_divergence_system():
    """测试背离交易信号系统"""
    print("=" * 60)
    print("背离交易信号系统测试")
    print("=" * 60)
    
    # 初始化数据管理器
    try:
        data_manager = StockDataManager()
        print("[OK] 数据管理器初始化成功")
    except Exception as e:
        print(f"[ERROR] 数据管理器初始化失败: {e}")
        return
    
    # 获取测试数据
    test_codes = ["513260.SH", "159509.SZ", "512890.SH"]  # 恒生科技ETF, 纳指科技ETF, 红利低波ETF
    
    for ts_code in test_codes:
        print(f"\n{'='*40}")
        print(f"测试基金: {ts_code}")
        print(f"{'='*40}")
        
        try:
            # 获取数据
            df = data_manager.get_fund_data(ts_code, start_date="20240101")
            if df.empty:
                print(f"[ERROR] 无法获取 {ts_code} 的数据")
                continue

            print(f"[OK] 获取到 {len(df)} 条数据记录")
            
            # 测试技术指标计算
            print("\n--- 技术指标计算测试 ---")
            test_technical_indicators(df)
            
            # 测试背离检测
            print("\n--- 背离检测测试 ---")
            divergence_results = test_divergence_detection(df)
            
            # 测试信号生成
            print("\n--- 信号生成测试 ---")
            buy_signals, sell_signals, stats = test_signal_generation(df)
            
            # 测试回测
            print("\n--- 回测测试 ---")
            backtest_results = test_backtesting(df, buy_signals, sell_signals)
            
            # 测试可视化
            print("\n--- 可视化测试 ---")
            test_visualization(ts_code, df)
            
            print(f"\n[OK] {ts_code} 测试完成")

        except Exception as e:
            print(f"[ERROR] {ts_code} 测试失败: {e}")
            import traceback
            traceback.print_exc()

def test_technical_indicators(df):
    """测试技术指标计算"""
    try:
        from indicator_analysis import TechnicalIndicator
        
        # 测试RSI计算
        rsi = TechnicalIndicator.calculate_rsi(df['close'].values)
        print(f"[OK] RSI计算成功，有效值数量: {np.sum(~np.isnan(rsi))}")

        # 测试KDJ计算
        k, d, j = TechnicalIndicator.calculate_kdj(
            df['high'].values, df['low'].values, df['close'].values
        )
        print(f"[OK] KDJ计算成功，K值有效数量: {np.sum(~np.isnan(k))}")

        # 测试MACD计算
        dif, dea, macd = TechnicalIndicator.calculate_macd(df['close'].values)
        print(f"[OK] MACD计算成功，DIF有效数量: {np.sum(~np.isnan(dif))}")

        # 测试峰谷检测
        peaks, troughs, peak_info, trough_info = TechnicalIndicator.detect_peaks_and_troughs(
            df['close'].values
        )
        print(f"[OK] 峰谷检测成功，峰值: {len(peaks)}个，谷值: {len(troughs)}个")

    except Exception as e:
        print(f"[ERROR] 技术指标计算失败: {e}")

def test_divergence_detection(df):
    """测试背离检测"""
    try:
        # 计算技术指标
        df_with_indicators = SignalDetector._calculate_all_indicators(df.copy())
        
        # 检测多指标背离
        divergence_results = SignalDetector.detect_multi_indicator_divergence(
            df_with_indicators, 
            indicators=['rsi', 'macd_dif', 'kdj_k']
        )
        
        print(f"[OK] 背离检测成功，检测到以下指标:")
        for indicator, results in divergence_results.items():
            bullish_count = results['bullish_divergence'].sum()
            bearish_count = results['bearish_divergence'].sum()
            print(f"  - {indicator}: 底背离 {bullish_count}个，顶背离 {bearish_count}个")

        return divergence_results

    except Exception as e:
        print(f"[ERROR] 背离检测失败: {e}")
        return {}

def test_signal_generation(df):
    """测试信号生成"""
    try:
        # 生成背离交易信号
        buy_signals, sell_signals, stats = SignalDetector.generate_divergence_trading_signals(
            df.copy(),
            min_signal_interval=5,
            volume_confirmation=False,  # 关闭成交量确认以便测试
            min_quality_score=0.3,
            indicators=['rsi', 'macd_dif', 'kdj_k']
        )
        
        print(f"[OK] 信号生成成功:")
        print(f"  - 买入信号: {len(buy_signals)}个")
        print(f"  - 卖出信号: {len(sell_signals)}个")
        print(f"  - 平均买入信号质量: {stats.get('avg_buy_quality', 0):.3f}")
        print(f"  - 平均卖出信号质量: {stats.get('avg_sell_quality', 0):.3f}")

        return buy_signals, sell_signals, stats

    except Exception as e:
        print(f"[ERROR] 信号生成失败: {e}")
        return pd.DataFrame(), pd.DataFrame(), {}

def test_backtesting(df, buy_signals, sell_signals):
    """测试回测功能"""
    try:
        if buy_signals.empty and sell_signals.empty:
            print("[WARN] 无信号可回测")
            return {}

        # 初始化回测器
        backtester = DivergenceBacktester(
            initial_capital=100000,
            commission_rate=0.001,
            slippage=0.001
        )

        # 执行回测
        results = backtester.backtest_divergence_signals(
            df, buy_signals, sell_signals,
            holding_period=10,
            stop_loss_pct=0.05,
            take_profit_pct=0.10
        )

        print(f"[OK] 回测完成:")
        print(f"  - 总交易次数: {results['total_trades']}")
        print(f"  - 胜率: {results['win_rate']:.2%}")
        print(f"  - 总收益率: {results['total_return']:.2%}")
        print(f"  - 最大回撤: {results['max_drawdown']:.2%}")
        print(f"  - 盈亏比: {results['profit_factor']:.2f}")

        return results

    except Exception as e:
        print(f"[ERROR] 回测失败: {e}")
        return {}

def test_visualization(ts_code, df):
    """测试可视化功能"""
    try:
        # 创建可视化器
        visualizer = StockEchartVisualizer()
        
        # 生成图表
        chart = visualizer.plot_stock_chart(
            ts_code,
            display_points=min(200, len(df)),
            force_refresh=False,
            detect_patterns=True
        )
        
        if chart is not None:
            # 保存图表
            output_file = f"cache/divergence_test_{ts_code.replace('.', '_')}.html"
            chart.render(output_file)
            print(f"[OK] 图表生成成功，保存至: {output_file}")
        else:
            print("[WARN] 图表生成返回None")

    except Exception as e:
        print(f"[ERROR] 可视化失败: {e}")

if __name__ == "__main__":
    test_divergence_system()
    print("\n" + "="*60)
    print("测试完成！")
    print("="*60)
