#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的背离信号生成
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from indicator_analysis import StockDataManager, SignalDetector

def main():
    print("测试修复后的背离信号生成")
    print("=" * 50)
    
    # 获取数据
    data_manager = StockDataManager()
    df = data_manager.get_fund_data("512890.SH", start_date="20240101")
    
    print(f"数据长度: {len(df)}")
    
    # 生成背离交易信号
    buy_df, sell_df, stats = SignalDetector.generate_divergence_trading_signals(
        df,
        min_signal_interval=3,
        volume_confirmation=False,
        min_quality_score=0.2,
        indicators=['rsi', 'macd_dif', 'kdj_k']
    )
    
    print(f"\n信号生成结果:")
    print(f"买入信号: {len(buy_df)}个")
    print(f"卖出信号: {len(sell_df)}个")
    
    if len(buy_df) > 0:
        print(f"\n买入信号详情:")
        for _, row in buy_df.iterrows():
            print(f"  日期: {row['trade_date']}")
            print(f"  价格: {row['close']:.4f}")
            print(f"  质量: {row.get('quality_score', 'N/A'):.3f}")
            print(f"  强度: {row.get('signal_strength', 'N/A'):.3f}")
            print()
    
    if len(sell_df) > 0:
        print(f"\n卖出信号详情:")
        for _, row in sell_df.iterrows():
            print(f"  日期: {row['trade_date']}")
            print(f"  价格: {row['close']:.4f}")
            print(f"  质量: {row.get('quality_score', 'N/A'):.3f}")
            print(f"  强度: {row.get('signal_strength', 'N/A'):.3f}")
            print()
    
    print(f"统计信息:")
    print(f"  平均买入质量: {stats.get('avg_buy_quality', 0):.3f}")
    print(f"  平均卖出质量: {stats.get('avg_sell_quality', 0):.3f}")
    
    # 测试可视化
    print(f"\n生成可视化图表...")
    try:
        from indicator_analysis import StockEchartVisualizer
        visualizer = StockEchartVisualizer()
        
        chart = visualizer.plot_stock_chart(
            "512890.SH",
            display_points=350,  # 增加显示点数以包含更多历史数据
            force_refresh=False,
            detect_patterns=False,
            show_traditional_signals=False,  # 不显示传统信号
            show_divergence_signals=True     # 只显示背离信号
        )
        
        if chart is not None:
            output_file = "cache/test_fix_divergence.html"
            chart.render(output_file)
            print(f"图表保存至: {output_file}")
            print(f"预期显示: {len(buy_df)}个买入信号, {len(sell_df)}个卖出信号")
        else:
            print("图表生成失败")
            
    except Exception as e:
        print(f"可视化失败: {e}")

if __name__ == "__main__":
    main()
