#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试可视化中的背离检测
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from indicator_analysis import StockDataManager, SignalDetector

def main():
    print("测试可视化中的背离检测")
    print("=" * 50)
    
    # 获取数据
    data_manager = StockDataManager()
    df = data_manager.get_fund_data("512890.SH", start_date="20240101")
    
    print(f"原始数据长度: {len(df)}")
    
    # 模拟可视化函数中的数据处理
    # 1. 限制显示点数（使用更大的数值以包含更多历史数据）
    display_points = min(350, len(df))  # 增加到350以包含更多历史数据
    df = df.tail(display_points).copy()
    print(f"限制后数据长度: {len(df)}")
    
    # 2. 重置索引
    df = df.reset_index(drop=True)
    print(f"重置索引后数据长度: {len(df)}")
    
    # 3. 计算技术指标
    df_with_indicators = SignalDetector._calculate_all_indicators(df.copy())
    print(f"添加指标后数据长度: {len(df_with_indicators)}")
    
    # 4. 检测背离
    divergence_results = SignalDetector.detect_multi_indicator_divergence(
        df_with_indicators,
        indicators=['rsi', 'macd_dif', 'kdj_k']
    )
    
    print(f"\n背离检测结果:")
    for indicator, results in divergence_results.items():
        bullish_count = results['bullish_divergence'].sum()
        bearish_count = results['bearish_divergence'].sum()
        print(f"{indicator}: 底背离 {bullish_count}个, 顶背离 {bearish_count}个")
    
    # 5. 生成交易信号
    buy_df, sell_df, stats = SignalDetector.generate_divergence_trading_signals(
        df_with_indicators,
        min_signal_interval=3,
        volume_confirmation=False,
        min_quality_score=0.3,
        indicators=['rsi', 'macd_dif', 'kdj_k']
    )
    
    print(f"\n交易信号结果:")
    print(f"买入信号: {len(buy_df)}个")
    print(f"卖出信号: {len(sell_df)}个")
    
    if len(buy_df) > 0:
        print(f"买入信号日期: {buy_df['trade_date'].tolist()}")
    
    if len(sell_df) > 0:
        print(f"卖出信号日期: {sell_df['trade_date'].tolist()}")
    
    print(f"\n统计信息: {stats}")
    
    # 检查数据范围
    print(f"\n数据范围检查:")
    print(f"日期范围: {df['trade_date'].min()} 到 {df['trade_date'].max()}")
    print(f"价格范围: {df['close'].min():.4f} 到 {df['close'].max():.4f}")

if __name__ == "__main__":
    main()
